import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { SERVICES } from '@kqick/shared-lib';

@Injectable()
export class MerchantService {
  constructor(
    @Inject(SERVICES.MERCHANT) private readonly merchantClient: ClientProxy,
  ) {}

  async findAll(query: {
    page?: number;
    limit?: number;
    search?: string;
    status?: string;
  }) {
    return firstValueFrom(
      this.merchantClient.send('find_all_merchants', query),
    );
  }

  async findById(id: string) {
    return firstValueFrom(
      this.merchantClient.send('find_merchant_by_id', { id }),
    );
  }

  async updateMerchantStatus(id: string, status: string) {
    return firstValueFrom(
      this.merchantClient.send('update_merchant_status', { id, status }),
    );
  }

  async deleteMerchant(id: string) {
    return firstValueFrom(
      this.merchantClient.send('delete_merchant', { id }),
    );
  }

  async getMerchantInventory(merchantId: string, query: {
    page?: number;
    limit?: number;
  }) {
    return firstValueFrom(
      this.merchantClient.send('get_merchant_inventory', { merchantId, ...query }),
    );
  }

  async getMerchantAnalytics() {
    return firstValueFrom(
      this.merchantClient.send('get_merchant_analytics', {}),
    );
  }
}

import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { SERVICES } from '@kqick/shared-lib';

@Injectable()
export class AnalyticsService {
  constructor(
    @Inject(SERVICES.PRODUCT) private readonly productClient: ClientProxy,
    @Inject(SERVICES.MERCHANT) private readonly merchantClient: ClientProxy,
  ) {}

  async getDashboardAnalytics() {
    try {
      const [productStats, merchantStats] = await Promise.all([
        this.getProductStats(),
        this.getMerchantStats(),
      ]);

      return {
        products: productStats,
        merchants: merchantStats,
        summary: {
          totalProducts: productStats.total,
          totalMerchants: merchantStats.total,
          activeMerchants: merchantStats.active,
          pendingMerchants: merchantStats.pending,
        },
      };
    } catch (error) {
      return {
        products: { total: 0, active: 0, categories: 0 },
        merchants: { total: 0, active: 0, pending: 0 },
        summary: {
          totalProducts: 0,
          totalMerchants: 0,
          activeMerchants: 0,
          pendingMerchants: 0,
        },
      };
    }
  }

  async getProductAnalytics(startDate?: string, endDate?: string) {
    try {
      return await firstValueFrom(
        this.productClient.send('get_product_analytics', { startDate, endDate }),
      );
    } catch (error) {
      return {
        totalProducts: 0,
        newProducts: 0,
        categoriesBreakdown: [],
        brandsBreakdown: [],
      };
    }
  }

  async getMerchantAnalytics(startDate?: string, endDate?: string) {
    try {
      return await firstValueFrom(
        this.merchantClient.send('get_merchant_analytics', { startDate, endDate }),
      );
    } catch (error) {
      return {
        totalMerchants: 0,
        newMerchants: 0,
        activeMerchants: 0,
        statusBreakdown: [],
      };
    }
  }

  async getSalesAnalytics(startDate?: string, endDate?: string, groupBy: 'day' | 'week' | 'month' = 'day') {
    try {
      return await firstValueFrom(
        this.merchantClient.send('get_sales_analytics', { startDate, endDate, groupBy }),
      );
    } catch (error) {
      return {
        totalSales: 0,
        salesData: [],
        growth: 0,
      };
    }
  }

  async getTopProducts(limit: number = 10, period?: string) {
    try {
      return await firstValueFrom(
        this.productClient.send('get_top_products', { limit, period }),
      );
    } catch (error) {
      return [];
    }
  }

  async getTopMerchants(limit: number = 10, period?: string) {
    try {
      return await firstValueFrom(
        this.merchantClient.send('get_top_merchants', { limit, period }),
      );
    } catch (error) {
      return [];
    }
  }

  private async getProductStats() {
    try {
      const [totalProducts, categories] = await Promise.all([
        firstValueFrom(
          this.productClient.send('find_all_universal_products', { page: 1, limit: 1 }),
        ),
        firstValueFrom(
          this.productClient.send('get_universal_product_categories', {}),
        ),
      ]);

      return {
        total: totalProducts.total || 0,
        active: totalProducts.data?.filter((p: any) => p.isActive).length || 0,
        categories: categories.length || 0,
      };
    } catch (error) {
      return { total: 0, active: 0, categories: 0 };
    }
  }

  private async getMerchantStats() {
    try {
      const merchants = await firstValueFrom(
        this.merchantClient.send('find_all_merchants', { page: 1, limit: 1000 }),
      );

      const total = merchants.total || 0;
      const active = merchants.data?.filter((m: any) => m.status === 'active').length || 0;
      const pending = merchants.data?.filter((m: any) => m.status === 'pending').length || 0;

      return { total, active, pending };
    } catch (error) {
      return { total: 0, active: 0, pending: 0 };
    }
  }
}

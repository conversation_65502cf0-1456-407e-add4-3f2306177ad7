import { MerchantInventoryService } from '../services/merchant-inventory.service';
import { CreateMerchantInventoryDto, UpdateMerchantInventoryDto } from '../dto/universal-product.dto';
export declare class MerchantInventoryController {
    private readonly merchantInventoryService;
    constructor(merchantInventoryService: MerchantInventoryService);
    addProductToInventory(data: {
        merchantId: string;
        createDto: CreateMerchantInventoryDto;
    }): Promise<import("../schemas/merchant-inventory.schema").MerchantInventory>;
    getMerchantInventory(data: {
        merchantId: string;
        page?: number;
        limit?: number;
        search?: string;
        category?: string;
    }): Promise<PaginatedResponse<any>>;
    updateMerchantInventory(data: {
        merchantId: string;
        inventoryId: string;
        updateDto: UpdateMerchantInventoryDto;
    }): Promise<import("../schemas/merchant-inventory.schema").MerchantInventory>;
    removeMerchantInventory(data: {
        merchantId: string;
        inventoryId: string;
    }): Promise<void>;
    getMerchantInventoryByProduct(data: {
        merchantId: string;
        universalProductId: string;
    }): Promise<import("../schemas/merchant-inventory.schema").MerchantInventory | null>;
    updateStock(data: {
        merchantId: string;
        universalProductId: string;
        stockChange: number;
    }): Promise<import("../schemas/merchant-inventory.schema").MerchantInventory>;
    getLowStockItems(data: {
        merchantId: string;
    }): Promise<import("../schemas/merchant-inventory.schema").MerchantInventory[]>;
}

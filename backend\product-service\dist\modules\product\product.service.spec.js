"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const testing_1 = require("@nestjs/testing");
const mongoose_1 = require("@nestjs/mongoose");
const product_service_1 = require("./product.service");
const product_schema_1 = require("./schemas/product.schema");
const shared_1 = require("../../types/shared");
const mockProduct = {
    id: 'product-1',
    name: 'Test Product',
    description: 'Test Description',
    category: 'Test Category',
    basePrice: 100,
    merchantId: 'merchant-1',
    isAvailable: true,
    createdAt: new Date(),
    updatedAt: new Date(),
};
describe('ProductService', () => {
    let service;
    let model;
    const mockProductModel = {
        new: jest.fn().mockResolvedValue(mockProduct),
        constructor: jest.fn().mockResolvedValue(mockProduct),
        find: jest.fn(),
        findOne: jest.fn(),
        findOneAndUpdate: jest.fn(),
        deleteOne: jest.fn(),
        distinct: jest.fn(),
        save: jest.fn(),
        exec: jest.fn(),
    };
    beforeEach(async () => {
        const module = await testing_1.Test.createTestingModule({
            providers: [
                product_service_1.ProductService,
                {
                    provide: (0, mongoose_1.getModelToken)(product_schema_1.Product.name),
                    useValue: mockProductModel,
                },
            ],
        }).compile();
        service = module.get(product_service_1.ProductService);
        model = module.get((0, mongoose_1.getModelToken)(product_schema_1.Product.name));
    });
    afterEach(() => {
        jest.clearAllMocks();
    });
    it('should be defined', () => {
        expect(service).toBeDefined();
    });
    describe('create', () => {
        it('should create a product', async () => {
            const createProductDto = {
                name: 'Test Product',
                description: 'Test Description',
                category: 'Test Category',
                basePrice: 100,
            };
            const merchantId = 'merchant-1';
            mockProductModel.save.mockResolvedValueOnce(mockProduct);
            const result = await service.create(merchantId, createProductDto);
            expect(result).toEqual(mockProduct);
            expect(mockProductModel.new).toHaveBeenCalledWith(Object.assign(Object.assign({}, createProductDto), { merchantId }));
        });
    });
    describe('findOne', () => {
        it('should return a product', async () => {
            mockProductModel.findOne.mockReturnValue({
                exec: jest.fn().mockResolvedValueOnce(mockProduct),
            });
            const result = await service.findOne('merchant-1', 'product-1');
            expect(result).toEqual(mockProduct);
            expect(mockProductModel.findOne).toHaveBeenCalledWith({
                _id: 'product-1',
                merchantId: 'merchant-1',
            });
        });
        it('should throw NotFoundError if product is not found', async () => {
            mockProductModel.findOne.mockReturnValue({
                exec: jest.fn().mockResolvedValueOnce(null),
            });
            await expect(service.findOne('merchant-1', 'product-1')).rejects.toThrow(shared_1.NotFoundError);
        });
    });
});
//# sourceMappingURL=product.service.spec.js.map
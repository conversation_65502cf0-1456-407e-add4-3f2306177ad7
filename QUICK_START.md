# 🚀 KQICK Quick Start Guide

## Prerequisites

- **Node.js** (v18 or higher)
- **Flutter** (latest stable version)
- **MongoDB Atlas** account (already configured)

## 🎯 Quick Setup (Windows)

### Option 1: Automated Setup
```powershell
# Run the setup script
.\setup-kqick.ps1
```

### Option 2: Manual Setup

1. **Install Backend Dependencies**
   ```bash
   # Shared library
   cd backend/shared-lib
   npm install

   # Product service
   cd ../product-service
   npm install

   # Admin service
   cd ../admin-service
   npm install
   ```

2. **Install Frontend Dependencies**
   ```bash
   # Admin dashboard
   cd admin-dashboard
   npm install

   # Merchant app
   cd merchant_app
   flutter pub get
   ```

3. **Setup Environment Files**
   ```bash
   # Copy environment template
   cp backend/.env.example backend/.env
   
   # Create admin dashboard env
   echo "REACT_APP_API_URL=http://localhost:3004" > admin-dashboard/.env
   ```

## 🗄️ Database Setup

1. **Seed the Database**
   ```bash
   cd backend/scripts
   npm install
   npm run seed
   ```

   This will create:
   - Super admin account: `<EMAIL>` / `KqickAdmin123!`
   - Sample universal products
   - Initial categories and brands

## 🏃‍♂️ Running the Services

### Backend Services

1. **Product Service** (Port 3002)
   ```bash
   cd backend/product-service
   npm run start:dev
   ```

2. **Admin Service** (Port 3004)
   ```bash
   cd backend/admin-service
   npm run start:dev
   ```

### Frontend Applications

3. **Admin Dashboard** (Port 3000)
   ```bash
   cd admin-dashboard
   npm start
   ```

4. **Merchant App** (Flutter)
   ```bash
   cd merchant_app
   flutter run
   ```

## 🔐 Default Login Credentials

### Admin Dashboard
- **URL**: http://localhost:3000
- **Email**: <EMAIL>
- **Password**: KqickAdmin123!

## 📱 Testing the System

### 1. Admin Dashboard
1. Open http://localhost:3000
2. Login with admin credentials
3. Navigate through:
   - Dashboard (analytics overview)
   - Products (universal catalog)
   - Merchants (approval workflow)
   - Admin Management (user management)

### 2. API Testing
Test the APIs directly:

```bash
# Get universal products
curl http://localhost:3002/universal-products

# Admin login
curl -X POST http://localhost:3004/admin/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"KqickAdmin123!"}'
```

### 3. Merchant App
1. Run `flutter run` in merchant_app directory
2. Test product browsing
3. Test inventory management
4. Test order processing

## 🔧 Configuration

### MongoDB Atlas
Your connection is already configured with:
- **Database**: kqick_admin, kqick_products
- **Connection**: Cluster0.3vszc.mongodb.net

### Environment Variables
Key variables in `backend/.env`:
```env
MONGODB_URI=mongodb+srv://lesibamosese03:<EMAIL>/...
JWT_SECRET=kqick-super-secret-jwt-key-change-in-production
NODE_ENV=development
```

## 🎯 Key Features to Test

### Universal Product Management
- ✅ Create/edit/delete products (admin only)
- ✅ Product categories and brands
- ✅ Search and filtering
- ✅ Barcode and SKU management

### Merchant Workflow
- ✅ Merchant registration
- ✅ Admin approval process
- ✅ Product catalog access
- ✅ Inventory management

### Admin System
- ✅ Role-based access control
- ✅ Analytics dashboard
- ✅ User management
- ✅ System monitoring

## 🚨 Security Notes

**IMPORTANT**: Change these immediately after testing:
1. MongoDB credentials
2. JWT secret key
3. Default admin password

## 📚 Documentation

- **Admin System**: See `ADMIN_SYSTEM_README.md`
- **API Documentation**: Check individual service folders
- **Flutter App**: See `merchant_app/README.md`

## 🐛 Troubleshooting

### Common Issues

1. **MongoDB Connection Error**
   - Check internet connection
   - Verify MongoDB Atlas credentials
   - Ensure IP whitelist includes your IP

2. **Port Already in Use**
   ```bash
   # Kill process on port
   npx kill-port 3002
   npx kill-port 3004
   ```

3. **Flutter Issues**
   ```bash
   flutter doctor
   flutter clean
   flutter pub get
   ```

### Logs
Check service logs for detailed error information:
- Product Service: `backend/product-service/logs/`
- Admin Service: `backend/admin-service/logs/`

## 🎉 Success!

If everything is running correctly, you should have:
- ✅ Product service on http://localhost:3002
- ✅ Admin service on http://localhost:3004  
- ✅ Admin dashboard on http://localhost:3000
- ✅ Merchant Flutter app running
- ✅ MongoDB Atlas connected
- ✅ Sample data loaded

## 🔄 Next Steps

1. **Customize Products**: Add your own product categories
2. **Test Merchant Flow**: Register test merchants
3. **Explore Analytics**: Check dashboard metrics
4. **API Integration**: Connect additional services
5. **Security**: Update credentials and secrets

Happy coding with KQICK! 🌟

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AdminService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const jwt_1 = require("@nestjs/jwt");
const bcrypt = require("bcrypt");
const admin_schema_1 = require("../schemas/admin.schema");
let AdminService = class AdminService {
    constructor(adminModel, jwtService) {
        this.adminModel = adminModel;
        this.jwtService = jwtService;
    }
    async createAdmin(createAdminDto) {
        const existingAdmin = await this.adminModel.findOne({ email: createAdminDto.email });
        if (existingAdmin) {
            throw new common_1.ConflictException('Admin with this email already exists');
        }
        const hashedPassword = await bcrypt.hash(createAdminDto.password, 12);
        const admin = new this.adminModel({
            ...createAdminDto,
            password: hashedPassword,
            permissions: createAdminDto.permissions || this.getDefaultPermissions(createAdminDto.role),
        });
        const savedAdmin = await admin.save();
        return this.toResponseDto(savedAdmin);
    }
    async login(loginDto) {
        const admin = await this.adminModel.findOne({ email: loginDto.email });
        if (!admin || !admin.isActive) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        const isPasswordValid = await bcrypt.compare(loginDto.password, admin.password);
        if (!isPasswordValid) {
            throw new common_1.UnauthorizedException('Invalid credentials');
        }
        admin.lastLogin = new Date();
        await admin.save();
        const payload = { sub: admin._id, email: admin.email, role: admin.role };
        const accessToken = this.jwtService.sign(payload);
        const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });
        admin.refreshToken = refreshToken;
        await admin.save();
        return {
            admin: this.toResponseDto(admin),
            accessToken,
            refreshToken,
        };
    }
    async refreshToken(refreshToken) {
        try {
            const payload = this.jwtService.verify(refreshToken);
            const admin = await this.adminModel.findById(payload.sub);
            if (!admin || admin.refreshToken !== refreshToken || !admin.isActive) {
                throw new common_1.UnauthorizedException('Invalid refresh token');
            }
            const newPayload = { sub: admin._id, email: admin.email, role: admin.role };
            const newAccessToken = this.jwtService.sign(newPayload);
            const newRefreshToken = this.jwtService.sign(newPayload, { expiresIn: '7d' });
            admin.refreshToken = newRefreshToken;
            await admin.save();
            return {
                accessToken: newAccessToken,
                refreshToken: newRefreshToken,
            };
        }
        catch (error) {
            throw new common_1.UnauthorizedException('Invalid refresh token');
        }
    }
    async logout(adminId) {
        await this.adminModel.findByIdAndUpdate(adminId, { refreshToken: null });
    }
    async findAll() {
        const admins = await this.adminModel.find().sort({ createdAt: -1 });
        return admins.map(admin => this.toResponseDto(admin));
    }
    async findById(id) {
        const admin = await this.adminModel.findById(id);
        if (!admin) {
            throw new common_1.NotFoundException('Admin not found');
        }
        return this.toResponseDto(admin);
    }
    async updateAdmin(id, updateAdminDto) {
        const admin = await this.adminModel.findByIdAndUpdate(id, updateAdminDto, { new: true });
        if (!admin) {
            throw new common_1.NotFoundException('Admin not found');
        }
        return this.toResponseDto(admin);
    }
    async changePassword(adminId, changePasswordDto) {
        const admin = await this.adminModel.findById(adminId);
        if (!admin) {
            throw new common_1.NotFoundException('Admin not found');
        }
        const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, admin.password);
        if (!isCurrentPasswordValid) {
            throw new common_1.UnauthorizedException('Current password is incorrect');
        }
        const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 12);
        admin.password = hashedNewPassword;
        await admin.save();
    }
    async deleteAdmin(id) {
        const result = await this.adminModel.findByIdAndDelete(id);
        if (!result) {
            throw new common_1.NotFoundException('Admin not found');
        }
    }
    getDefaultPermissions(role) {
        switch (role) {
            case admin_schema_1.AdminRole.SUPER_ADMIN:
                return Object.values(admin_schema_1.AdminPermission);
            case admin_schema_1.AdminRole.ADMIN:
                return [
                    admin_schema_1.AdminPermission.MANAGE_PRODUCTS,
                    admin_schema_1.AdminPermission.MANAGE_MERCHANTS,
                    admin_schema_1.AdminPermission.MANAGE_ORDERS,
                    admin_schema_1.AdminPermission.VIEW_ANALYTICS,
                ];
            case admin_schema_1.AdminRole.MODERATOR:
                return [
                    admin_schema_1.AdminPermission.MANAGE_PRODUCTS,
                    admin_schema_1.AdminPermission.VIEW_ANALYTICS,
                ];
            default:
                return [];
        }
    }
    toResponseDto(admin) {
        return {
            id: admin._id.toString(),
            email: admin.email,
            firstName: admin.firstName,
            lastName: admin.lastName,
            role: admin.role,
            permissions: admin.permissions,
            isActive: admin.isActive,
            lastLogin: admin.lastLogin,
            profileImage: admin.profileImage,
            phoneNumber: admin.phoneNumber,
            createdAt: admin.createdAt,
            updatedAt: admin.updatedAt,
        };
    }
};
exports.AdminService = AdminService;
exports.AdminService = AdminService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(admin_schema_1.Admin.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        jwt_1.JwtService])
], AdminService);
//# sourceMappingURL=admin.service.js.map
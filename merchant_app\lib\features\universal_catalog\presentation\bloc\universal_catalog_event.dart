import 'package:equatable/equatable.dart';

abstract class UniversalCatalogEvent extends Equatable {
  const UniversalCatalogEvent();

  @override
  List<Object?> get props => [];
}

class LoadUniversalProducts extends UniversalCatalogEvent {
  final bool refresh;
  final String? search;
  final String? category;
  final String? subcategory;
  final String? brand;
  final List<String>? tags;
  final double? minPrice;
  final double? maxPrice;
  final String? sortBy;
  final String? sortOrder;

  const LoadUniversalProducts({
    this.refresh = false,
    this.search,
    this.category,
    this.subcategory,
    this.brand,
    this.tags,
    this.minPrice,
    this.maxPrice,
    this.sortBy,
    this.sortOrder,
  });

  @override
  List<Object?> get props => [
        refresh,
        search,
        category,
        subcategory,
        brand,
        tags,
        minPrice,
        maxPrice,
        sortBy,
        sortOrder,
      ];
}

class LoadMoreUniversalProducts extends UniversalCatalogEvent {
  const LoadMoreUniversalProducts();
}

class SearchUniversalProductByBarcode extends UniversalCatalogEvent {
  final String barcode;

  const SearchUniversalProductByBarcode(this.barcode);

  @override
  List<Object?> get props => [barcode];
}

class SearchUniversalProductBySku extends UniversalCatalogEvent {
  final String sku;

  const SearchUniversalProductBySku(this.sku);

  @override
  List<Object?> get props => [sku];
}

class AddProductToInventory extends UniversalCatalogEvent {
  final String universalProductId;
  final double price;
  final int stock;
  final bool isAvailable;
  final int? minStock;
  final int? maxStock;
  final String? location;

  const AddProductToInventory({
    required this.universalProductId,
    required this.price,
    this.stock = 0,
    this.isAvailable = true,
    this.minStock,
    this.maxStock,
    this.location,
  });

  @override
  List<Object?> get props => [
        universalProductId,
        price,
        stock,
        isAvailable,
        minStock,
        maxStock,
        location,
      ];
}

class LoadMerchantInventory extends UniversalCatalogEvent {
  final bool refresh;
  final String? search;
  final String? category;

  const LoadMerchantInventory({
    this.refresh = false,
    this.search,
    this.category,
  });

  @override
  List<Object?> get props => [refresh, search, category];
}

class LoadMoreMerchantInventory extends UniversalCatalogEvent {
  const LoadMoreMerchantInventory();
}

class UpdateMerchantInventory extends UniversalCatalogEvent {
  final String inventoryId;
  final double? price;
  final int? stock;
  final bool? isAvailable;
  final int? minStock;
  final int? maxStock;
  final String? location;

  const UpdateMerchantInventory({
    required this.inventoryId,
    this.price,
    this.stock,
    this.isAvailable,
    this.minStock,
    this.maxStock,
    this.location,
  });

  @override
  List<Object?> get props => [
        inventoryId,
        price,
        stock,
        isAvailable,
        minStock,
        maxStock,
        location,
      ];
}

class RemoveMerchantInventory extends UniversalCatalogEvent {
  final String inventoryId;

  const RemoveMerchantInventory(this.inventoryId);

  @override
  List<Object?> get props => [inventoryId];
}

class UpdateStock extends UniversalCatalogEvent {
  final String universalProductId;
  final int stockChange;

  const UpdateStock({
    required this.universalProductId,
    required this.stockChange,
  });

  @override
  List<Object?> get props => [universalProductId, stockChange];
}

class LoadLowStockItems extends UniversalCatalogEvent {
  const LoadLowStockItems();
}

class LoadCategories extends UniversalCatalogEvent {
  const LoadCategories();
}

class LoadSubcategories extends UniversalCatalogEvent {
  final String? category;

  const LoadSubcategories(this.category);

  @override
  List<Object?> get props => [category];
}

class LoadBrands extends UniversalCatalogEvent {
  final String? category;
  final String? subcategory;

  const LoadBrands({this.category, this.subcategory});

  @override
  List<Object?> get props => [category, subcategory];
}

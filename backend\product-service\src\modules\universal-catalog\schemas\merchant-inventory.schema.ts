import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';

@Schema({ timestamps: true })
export class MerchantInventory extends Document {
  @Prop({ required: true, type: String })
  merchantId: string;

  @Prop({ required: true, type: MongooseSchema.Types.ObjectId, ref: 'UniversalProduct' })
  universalProductId: MongooseSchema.Types.ObjectId;

  @Prop({ required: true })
  price: number;

  @Prop({ required: true, default: 0 })
  stock: number;

  @Prop({ default: true })
  isAvailable: boolean;

  @Prop()
  minStock?: number;

  @Prop()
  maxStock?: number;

  @Prop()
  location?: string;
}

export const MerchantInventorySchema = SchemaFactory.createForClass(MerchantInventory);

// Create compound index to ensure one inventory record per merchant per product
MerchantInventorySchema.index({ merchantId: 1, universalProductId: 1 }, { unique: true });
MerchantInventorySchema.index({ merchantId: 1 });
MerchantInventorySchema.index({ universalProductId: 1 });
MerchantInventorySchema.index({ isAvailable: 1 });
MerchantInventorySchema.index({ stock: 1 });

import React, { useEffect } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Box,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { useMutation, useQuery } from 'react-query';
import { apiService } from '../../services/api';

interface ProductFormProps {
  open: boolean;
  onClose: () => void;
  product?: any;
  onSuccess: () => void;
}

interface ProductFormData {
  name: string;
  description: string;
  category: string;
  subcategory: string;
  brand: string;
  suggestedPrice: number;
  imageUrl?: string;
  barcode?: string;
  sku?: string;
  isActive: boolean;
  tags: string;
}

const ProductForm: React.FC<ProductFormProps> = ({
  open,
  onClose,
  product,
  onSuccess,
}) => {
  const isEdit = !!product;

  const { control, handleSubmit, reset, watch, setValue } = useForm<ProductFormData>({
    defaultValues: {
      name: '',
      description: '',
      category: '',
      subcategory: '',
      brand: '',
      suggestedPrice: 0,
      imageUrl: '',
      barcode: '',
      sku: '',
      isActive: true,
      tags: '',
    },
  });

  const selectedCategory = watch('category');

  const { data: categories } = useQuery('categories', apiService.getProductCategories);
  const { data: subcategories } = useQuery(
    ['subcategories', selectedCategory],
    () => apiService.getProductSubcategories(selectedCategory),
    { enabled: !!selectedCategory }
  );
  const { data: brands } = useQuery(
    ['brands', selectedCategory],
    () => apiService.getProductBrands(selectedCategory),
    { enabled: !!selectedCategory }
  );

  const createMutation = useMutation(apiService.createUniversalProduct, {
    onSuccess,
  });

  const updateMutation = useMutation(
    ({ id, data }: { id: string; data: any }) =>
      apiService.updateUniversalProduct(id, data),
    { onSuccess }
  );

  useEffect(() => {
    if (product) {
      reset({
        name: product.name || '',
        description: product.description || '',
        category: product.category || '',
        subcategory: product.subcategory || '',
        brand: product.brand || '',
        suggestedPrice: product.suggestedPrice || 0,
        imageUrl: product.imageUrl || '',
        barcode: product.barcode || '',
        sku: product.sku || '',
        isActive: product.isActive ?? true,
        tags: product.tags?.join(', ') || '',
      });
    } else {
      reset({
        name: '',
        description: '',
        category: '',
        subcategory: '',
        brand: '',
        suggestedPrice: 0,
        imageUrl: '',
        barcode: '',
        sku: '',
        isActive: true,
        tags: '',
      });
    }
  }, [product, reset]);

  const onSubmit = (data: ProductFormData) => {
    const formData = {
      ...data,
      tags: data.tags ? data.tags.split(',').map(tag => tag.trim()) : [],
    };

    if (isEdit) {
      updateMutation.mutate({ id: product.id || product._id, data: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const isLoading = createMutation.isLoading || updateMutation.isLoading;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{isEdit ? 'Edit Product' : 'Add New Product'}</DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Controller
                name="name"
                control={control}
                rules={{ required: 'Name is required' }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Product Name"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="brand"
                control={control}
                rules={{ required: 'Brand is required' }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Brand"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="description"
                control={control}
                rules={{ required: 'Description is required' }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Description"
                    multiline
                    rows={3}
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="category"
                control={control}
                rules={{ required: 'Category is required' }}
                render={({ field, fieldState }) => (
                  <FormControl fullWidth error={!!fieldState.error}>
                    <InputLabel>Category</InputLabel>
                    <Select {...field} label="Category">
                      {categories?.map((category: string) => (
                        <MenuItem key={category} value={category}>
                          {category}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="subcategory"
                control={control}
                rules={{ required: 'Subcategory is required' }}
                render={({ field, fieldState }) => (
                  <FormControl fullWidth error={!!fieldState.error}>
                    <InputLabel>Subcategory</InputLabel>
                    <Select {...field} label="Subcategory" disabled={!selectedCategory}>
                      {subcategories?.map((subcategory: string) => (
                        <MenuItem key={subcategory} value={subcategory}>
                          {subcategory}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={4}>
              <Controller
                name="suggestedPrice"
                control={control}
                rules={{ required: 'Price is required', min: 0 }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Suggested Price"
                    type="number"
                    InputProps={{ startAdornment: 'R' }}
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="barcode"
                control={control}
                render={({ field }) => (
                  <TextField {...field} fullWidth label="Barcode" />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="sku"
                control={control}
                render={({ field }) => (
                  <TextField {...field} fullWidth label="SKU" />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="imageUrl"
                control={control}
                render={({ field }) => (
                  <TextField {...field} fullWidth label="Image URL" />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="tags"
                control={control}
                render={({ field }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Tags (comma separated)"
                    helperText="Enter tags separated by commas"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Controller
                name="isActive"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Switch {...field} checked={field.value} />}
                    label="Active"
                  />
                )}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" disabled={isLoading}>
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default ProductForm;

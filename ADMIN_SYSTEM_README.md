# KQICK Admin System

## Overview

The KQICK Admin System consists of two main components:
1. **Admin Service** - NestJS backend service for admin operations
2. **Admin Dashboard** - React web application for admin interface

## Features

### Admin Service Backend

#### Authentication & Authorization
- JWT-based authentication
- Role-based access control (Super Admin, Admin, Moderator)
- Permission-based authorization
- Secure password hashing with bcrypt
- Token refresh mechanism

#### Admin Management
- Create, read, update, delete admin accounts
- Role and permission management
- Profile management
- Password change functionality

#### Universal Product Management
- Full CRUD operations for universal products
- Product categorization and search
- Barcode and SKU management
- Bulk import functionality
- Product analytics

#### Merchant Management
- View and manage merchant accounts
- Approve/reject merchant applications
- Suspend/activate merchants
- Merchant analytics and reporting

#### Analytics & Reporting
- Dashboard analytics
- Product performance metrics
- Merchant statistics
- Sales analytics
- Real-time data aggregation

### Admin Dashboard Frontend

#### Modern React Architecture
- TypeScript for type safety
- Material-UI for consistent design
- Redux Toolkit for state management
- React Query for data fetching
- React Hook Form for form handling

#### Key Features
- Responsive dashboard with analytics
- Universal product catalog management
- Merchant approval workflow
- Admin user management
- Real-time data visualization
- Advanced search and filtering

## Architecture

### Backend Services
```
admin-service/
├── src/
│   ├── modules/
│   │   ├── admin/           # Admin authentication & management
│   │   ├── universal-product/  # Product management
│   │   ├── merchant/        # Merchant management
│   │   └── analytics/       # Analytics & reporting
│   ├── config/             # Configuration
│   └── main.ts            # Application entry point
```

### Frontend Structure
```
admin-dashboard/
├── src/
│   ├── components/         # Reusable components
│   ├── pages/             # Page components
│   ├── services/          # API services
│   ├── store/             # Redux store
│   └── App.tsx           # Main application
```

## Setup Instructions

### Backend Setup

1. **Install Dependencies**
   ```bash
   cd backend/admin-service
   npm install
   ```

2. **Environment Variables**
   Create `.env` file:
   ```env
   PORT=3004
   MONGODB_URI=mongodb://localhost:27017/kqick_admin
   RABBITMQ_URL=amqp://localhost:5672
   REDIS_URL=redis://localhost:6379
   JWT_SECRET=your-secret-key
   JWT_EXPIRES_IN=24h
   ```

3. **Start the Service**
   ```bash
   npm run start:dev
   ```

### Frontend Setup

1. **Install Dependencies**
   ```bash
   cd admin-dashboard
   npm install
   ```

2. **Environment Variables**
   Create `.env` file:
   ```env
   REACT_APP_API_URL=http://localhost:3004
   ```

3. **Start the Dashboard**
   ```bash
   npm start
   ```

## API Endpoints

### Authentication
- `POST /admin/login` - Admin login
- `POST /admin/refresh` - Refresh token
- `POST /admin/logout` - Admin logout

### Admin Management
- `GET /admin` - List all admins
- `POST /admin` - Create new admin
- `GET /admin/profile` - Get current admin profile
- `PUT /admin/profile` - Update profile
- `PUT /admin/:id` - Update admin
- `DELETE /admin/:id` - Delete admin

### Universal Products
- `GET /universal-products` - List products
- `POST /universal-products` - Create product
- `GET /universal-products/:id` - Get product
- `PUT /universal-products/:id` - Update product
- `DELETE /universal-products/:id` - Delete product
- `GET /universal-products/categories` - Get categories
- `GET /universal-products/brands` - Get brands

### Merchants
- `GET /merchants` - List merchants
- `GET /merchants/:id` - Get merchant
- `PUT /merchants/:id/approve` - Approve merchant
- `PUT /merchants/:id/reject` - Reject merchant
- `PUT /merchants/:id/suspend` - Suspend merchant

### Analytics
- `GET /analytics/dashboard` - Dashboard analytics
- `GET /analytics/products` - Product analytics
- `GET /analytics/merchants` - Merchant analytics

## User Roles & Permissions

### Super Admin
- Full system access
- Manage all admins
- System configuration
- All permissions

### Admin
- Manage products and merchants
- View analytics
- Manage orders
- Cannot manage other admins

### Moderator
- Manage products
- View analytics
- Limited access

## Security Features

- JWT authentication with refresh tokens
- Role-based access control
- Permission-based authorization
- Password hashing with bcrypt
- CORS protection
- Input validation
- SQL injection prevention
- XSS protection

## Development Guidelines

### Backend
- Use TypeScript for type safety
- Follow NestJS conventions
- Implement proper error handling
- Add comprehensive logging
- Write unit tests
- Use DTOs for validation

### Frontend
- Use TypeScript consistently
- Follow React best practices
- Implement proper error boundaries
- Use React Query for data fetching
- Maintain responsive design
- Add loading states

## Deployment

### Docker Deployment
Both services include Dockerfiles for containerized deployment.

### Environment Configuration
- Production environment variables
- Database connection strings
- External service URLs
- Security configurations

## Monitoring & Logging

- Application logs
- Error tracking
- Performance monitoring
- User activity logs
- System health checks

## Future Enhancements

- Real-time notifications
- Advanced analytics
- Audit logging
- Multi-factor authentication
- API rate limiting
- Advanced search capabilities
- Export functionality
- Mobile admin app

## Support

For technical support or questions about the admin system, please contact the development team.

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const core_1 = require("@nestjs/core");
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const config_1 = require("@nestjs/config");
const app_module_1 = require("./app.module");
async function bootstrap() {
    const app = await core_1.NestFactory.create(app_module_1.AppModule);
    const configService = app.get(config_1.ConfigService);
    const rabbitmqUrl = configService.get('rabbitmq.url') || 'amqp://localhost:5672';
    const rabbitmqQueue = configService.get('rabbitmq.queue') || 'product_queue';
    app.connectMicroservice({
        transport: microservices_1.Transport.RMQ,
        options: {
            urls: [rabbitmqUrl],
            queue: rabbitmqQueue,
            queueOptions: {
                durable: true,
            },
        },
    });
    app.useGlobalPipes(new common_1.ValidationPipe({
        whitelist: true,
        transform: true,
        forbidNonWhitelisted: true,
    }));
    app.enableCors();
    await app.startAllMicroservices();
    const port = configService.get('port') || 3002;
    await app.listen(port);
}
bootstrap();
//# sourceMappingURL=main.js.map
import { Document, Schema as MongooseSchema } from 'mongoose';
export declare class MerchantInventory extends Document {
    merchantId: string;
    universalProductId: MongooseSchema.Types.ObjectId;
    price: number;
    stock: number;
    isAvailable: boolean;
    minStock?: number;
    maxStock?: number;
    location?: string;
}
export declare const MerchantInventorySchema: MongooseSchema<MerchantInventory, import("mongoose").Model<MerchantInventory, any, any, any, Document<unknown, any, MerchantInventory, any> & MerchantInventory & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, MerchantInventory, Document<unknown, {}, import("mongoose").FlatRecord<MerchantInventory>, {}> & import("mongoose").FlatRecord<MerchantInventory> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;

import { Document } from 'mongoose';
export declare enum AdminRole {
    SUPER_ADMIN = "super_admin",
    ADMIN = "admin",
    MODERATOR = "moderator"
}
export declare enum AdminPermission {
    MANAGE_PRODUCTS = "manage_products",
    MANAGE_MERCHANTS = "manage_merchants",
    MANAGE_ORDERS = "manage_orders",
    VIEW_ANALYTICS = "view_analytics",
    MANAGE_ADMINS = "manage_admins",
    SYSTEM_SETTINGS = "system_settings"
}
export declare class Admin extends Document {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: AdminRole;
    permissions: AdminPermission[];
    isActive: boolean;
    lastLogin?: Date;
    refreshToken?: string;
    profileImage?: string;
    phoneNumber?: string;
    createdAt?: Date;
    updatedAt?: Date;
}
export declare const AdminSchema: import("mongoose").Schema<Admin, import("mongoose").Model<Admin, any, any, any, Document<unknown, any, Admin> & Admin & {
    _id: import("mongoose").Types.ObjectId;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Admin, Document<unknown, {}, import("mongoose").FlatRecord<Admin>> & import("mongoose").FlatRecord<Admin> & {
    _id: import("mongoose").Types.ObjectId;
}>;

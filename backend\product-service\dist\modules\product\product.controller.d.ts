import { ProductService } from './product.service';
import { CreateProductDto, UpdateProductDto, UpdateInventoryDto } from './dto/product.dto';
import { SearchQuery } from '@kqick/shared-lib';
export declare class ProductController {
    private readonly productService;
    constructor(productService: ProductService);
    create(data: {
        merchantId: string;
        product: CreateProductDto;
    }): Promise<import("./schemas/product.schema").Product>;
    findAll(data: {
        merchantId: string;
        query: SearchQuery;
    }): Promise<PaginatedResponse<import("./schemas/product.schema").Product>>;
    findOne(data: {
        merchantId: string;
        id: string;
    }): Promise<import("./schemas/product.schema").Product>;
    update(data: {
        merchantId: string;
        id: string;
        product: UpdateProductDto;
    }): Promise<import("./schemas/product.schema").Product>;
    updateInventory(data: {
        merchantId: string;
        id: string;
        inventory: UpdateInventoryDto;
    }): Promise<import("./schemas/product.schema").Product>;
    remove(data: {
        merchantId: string;
        id: string;
    }): Promise<void>;
    getCategories(data: {
        merchantId: string;
    }): Promise<string[]>;
}

const { MongoClient } = require('mongodb');
const bcrypt = require('bcrypt');

// MongoDB connection string with your credentials
const MONGODB_URI = 'mongodb+srv://lesibamosese03:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0';

async function seedDatabase() {
  const client = new MongoClient(MONGODB_URI);
  
  try {
    await client.connect();
    console.log('🔗 Connected to MongoDB Atlas');

    // Seed Admin Database
    await seedAdminData(client);
    
    // Seed Product Database
    await seedProductData(client);
    
    console.log('✅ Database seeding completed successfully!');
  } catch (error) {
    console.error('❌ Error seeding database:', error);
  } finally {
    await client.close();
  }
}

async function seedAdminData(client) {
  const adminDb = client.db('kqick_admin');
  const adminsCollection = adminDb.collection('admins');
  
  // Check if super admin already exists
  const existingAdmin = await adminsCollection.findOne({ email: '<EMAIL>' });
  
  if (!existingAdmin) {
    const hashedPassword = await bcrypt.hash('KqickAdmin123!', 12);
    
    const superAdmin = {
      email: '<EMAIL>',
      password: hashedPassword,
      firstName: 'Super',
      lastName: 'Admin',
      role: 'super_admin',
      permissions: [
        'manage_products',
        'manage_merchants',
        'manage_orders',
        'view_analytics',
        'manage_admins',
        'system_settings'
      ],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    
    await adminsCollection.insertOne(superAdmin);
    console.log('✅ Created super admin account');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: KqickAdmin123!');
  } else {
    console.log('⚠️  Super admin already exists');
  }
}

async function seedProductData(client) {
  const productDb = client.db('kqick_products');
  const universalProductsCollection = productDb.collection('universalproducts');
  
  // Check if products already exist
  const existingProducts = await universalProductsCollection.countDocuments();
  
  if (existingProducts === 0) {
    const sampleProducts = [
      {
        name: 'Samsung Galaxy S24',
        description: 'Latest Samsung flagship smartphone with advanced AI features',
        category: 'Electronics',
        subcategory: 'Smartphones',
        brand: 'Samsung',
        suggestedPrice: 15999.99,
        imageUrl: 'https://images.samsung.com/is/image/samsung/p6pim/za/2401/gallery/za-galaxy-s24-s928-sm-s928bzadeub-thumb-*********',
        barcode: '*************',
        sku: 'SAM-S24-128GB-BLK',
        isActive: true,
        tags: ['smartphone', 'android', 'flagship', '5g'],
        specifications: {
          storage: '128GB',
          ram: '8GB',
          display: '6.2 inch',
          camera: '50MP',
          battery: '4000mAh'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'iPhone 15',
        description: 'Apple iPhone 15 with USB-C and advanced camera system',
        category: 'Electronics',
        subcategory: 'Smartphones',
        brand: 'Apple',
        suggestedPrice: 18999.99,
        imageUrl: 'https://store.storeimages.cdn-apple.com/4982/as-images.apple.com/is/iphone-15-finish-select-202309-6-1inch-pink',
        barcode: '0194253404057',
        sku: 'APL-IP15-128GB-PNK',
        isActive: true,
        tags: ['smartphone', 'ios', 'premium', '5g'],
        specifications: {
          storage: '128GB',
          ram: '6GB',
          display: '6.1 inch',
          camera: '48MP',
          battery: '3349mAh'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Coca-Cola 330ml',
        description: 'Classic Coca-Cola soft drink in 330ml can',
        category: 'Food & Beverage',
        subcategory: 'Soft Drinks',
        brand: 'Coca-Cola',
        suggestedPrice: 12.99,
        imageUrl: 'https://www.coca-cola.com/content/dam/onexp/za/en/brands/coca-cola/coca-cola-original-taste-330ml-can.png',
        barcode: '5449000000996',
        sku: 'COC-COLA-330ML',
        isActive: true,
        tags: ['beverage', 'soft drink', 'cola', 'refreshing'],
        specifications: {
          volume: '330ml',
          calories: '139',
          caffeine: '34mg'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'White Bread 700g',
        description: 'Fresh white bread loaf, perfect for sandwiches',
        category: 'Food & Beverage',
        subcategory: 'Bakery',
        brand: 'Albany',
        suggestedPrice: 16.99,
        imageUrl: 'https://www.albany.co.za/images/products/white-bread-700g.jpg',
        barcode: '6001087000123',
        sku: 'ALB-BREAD-WHITE-700G',
        isActive: true,
        tags: ['bread', 'bakery', 'fresh', 'daily'],
        specifications: {
          weight: '700g',
          slices: '20',
          shelf_life: '3 days'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      },
      {
        name: 'Nike Air Max 270',
        description: 'Comfortable running shoes with Air Max technology',
        category: 'Fashion & Apparel',
        subcategory: 'Footwear',
        brand: 'Nike',
        suggestedPrice: 2299.99,
        imageUrl: 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/awjogtdnqxniqqk0wpgf/air-max-270-shoes-KkLcGR.png',
        barcode: '0193655394567',
        sku: 'NIK-AM270-BLK-42',
        isActive: true,
        tags: ['shoes', 'running', 'sports', 'comfort'],
        specifications: {
          size: '42',
          color: 'Black/White',
          material: 'Mesh/Synthetic',
          technology: 'Air Max'
        },
        createdAt: new Date(),
        updatedAt: new Date()
      }
    ];
    
    await universalProductsCollection.insertMany(sampleProducts);
    console.log(`✅ Created ${sampleProducts.length} sample products`);
  } else {
    console.log('⚠️  Products already exist in database');
  }
}

// Run the seeding
seedDatabase().catch(console.error);

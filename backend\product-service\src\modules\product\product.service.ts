import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { Product } from './schemas/product.schema';
import { CreateProductDto, UpdateProductDto, UpdateInventoryDto } from './dto/product.dto';
import { NotFoundError, SearchQuery, PaginatedResponse } from '../../types/shared';

@Injectable()
export class ProductService {
  constructor(
    @InjectModel(Product.name) private readonly productModel: Model<Product>,
  ) {}

  async create(merchantId: string, createProductDto: CreateProductDto): Promise<Product> {
    const product = new this.productModel({
      ...createProductDto,
      merchantId,
    });
    return product.save();
  }

  async findAll(merchantId: string, query: SearchQuery): Promise<PaginatedResponse<Product>> {
    const { page = 1, limit = 20, search, category, sort, order = 'asc' } = query;
    const skip = (page - 1) * limit;

    const filter: any = { merchantId };
    if (search) {
      filter.$or = [
        { name: new RegExp(search, 'i') },
        { description: new RegExp(search, 'i') },
        { barcode: new RegExp(search, 'i') },
      ];
    }
    if (category) {
      filter.category = category;
    }

    const sortOptions: any = {};
    if (sort) {
      sortOptions[sort] = order === 'asc' ? 1 : -1;
    } else {
      sortOptions.updatedAt = -1;
    }

    const [products, total] = await Promise.all([
      this.productModel
        .find(filter)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.productModel.countDocuments(filter),
    ]);

    return {
      data: products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
      hasMore: total > skip + products.length,
    };
  }

  async findOne(merchantId: string, id: string): Promise<Product> {
    const product = await this.productModel.findOne({ _id: id, merchantId }).exec();
    if (!product) {
      throw new NotFoundError('Product not found');
    }
    return product;
  }

  async update(merchantId: string, id: string, updateProductDto: UpdateProductDto): Promise<Product> {
    const product = await this.productModel
      .findOneAndUpdate(
        { _id: id, merchantId },
        { ...updateProductDto, updatedAt: new Date() },
        { new: true },
      )
      .exec();

    if (!product) {
      throw new NotFoundError('Product not found');
    }
    return product;
  }

  async updateInventory(
    merchantId: string,
    id: string,
    updateInventoryDto: UpdateInventoryDto,
  ): Promise<Product> {
    const product = await this.productModel
      .findOneAndUpdate(
        { _id: id, merchantId },
        {
          basePrice: updateInventoryDto.price,
          isAvailable: updateInventoryDto.isAvailable ?? (updateInventoryDto.quantity > 0),
          updatedAt: new Date(),
        },
        { new: true },
      )
      .exec();

    if (!product) {
      throw new NotFoundError('Product not found');
    }
    return product;
  }

  async remove(merchantId: string, id: string): Promise<void> {
    const result = await this.productModel.deleteOne({ _id: id, merchantId }).exec();
    if (result.deletedCount === 0) {
      throw new NotFoundError('Product not found');
    }
  }

  async getCategories(merchantId: string): Promise<string[]> {
    const categories = await this.productModel.distinct('category', { merchantId }).exec();
    return categories.sort();
  }
}

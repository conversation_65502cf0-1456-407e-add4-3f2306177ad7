import { Document, Schema as MongooseSchema } from 'mongoose';
import { ProductVariant } from '../../../types/shared';
export declare class Product extends Document {
    name: string;
    description: string;
    category: string;
    basePrice: number;
    imageUrl?: string;
    barcode?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isAvailable: boolean;
    merchantId: string;
}
export declare const ProductSchema: MongooseSchema<Product, import("mongoose").Model<Product, any, any, any, Document<unknown, any, Product, any> & Product & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, Product, Document<unknown, {}, import("mongoose").FlatRecord<Product>, {}> & import("mongoose").FlatRecord<Product> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Add,
  Search,
  Edit,
  Delete,
  Visibility,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useSelector } from 'react-redux';
import { RootState } from '../../store/store';
import { apiService } from '../../services/api';
import AdminForm from './AdminForm';

const AdminManagement: React.FC = () => {
  const queryClient = useQueryClient();
  const { admin: currentAdmin } = useSelector((state: RootState) => state.auth);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedAdmin, setSelectedAdmin] = useState<any>(null);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [isViewOpen, setIsViewOpen] = useState(false);
  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);
  const [adminToDelete, setAdminToDelete] = useState<string | null>(null);

  const { data: admins, isLoading } = useQuery(
    'admins',
    apiService.getAdmins
  );

  const deleteMutation = useMutation(apiService.deleteAdmin, {
    onSuccess: () => {
      queryClient.invalidateQueries('admins');
      setDeleteConfirmOpen(false);
      setAdminToDelete(null);
    },
  });

  const handleEdit = (admin: any) => {
    setSelectedAdmin(admin);
    setIsFormOpen(true);
  };

  const handleView = (admin: any) => {
    setSelectedAdmin(admin);
    setIsViewOpen(true);
  };

  const handleDelete = (id: string) => {
    setAdminToDelete(id);
    setDeleteConfirmOpen(true);
  };

  const confirmDelete = () => {
    if (adminToDelete) {
      deleteMutation.mutate(adminToDelete);
    }
  };

  const handleAddNew = () => {
    setSelectedAdmin(null);
    setIsFormOpen(true);
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'super_admin':
        return 'error';
      case 'admin':
        return 'primary';
      case 'moderator':
        return 'secondary';
      default:
        return 'default';
    }
  };

  const canManageAdmin = (admin: any) => {
    if (currentAdmin?.role === 'super_admin') return true;
    if (currentAdmin?.role === 'admin' && admin.role !== 'super_admin') return true;
    return false;
  };

  const filteredAdmins = admins?.filter((admin: any) =>
    admin.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    admin.email.toLowerCase().includes(searchTerm.toLowerCase())
  ) || [];

  const columns: GridColDef[] = [
    {
      field: 'fullName',
      headerName: 'Name',
      width: 200,
      valueGetter: (params) => `${params.row.firstName} ${params.row.lastName}`,
    },
    { field: 'email', headerName: 'Email', width: 250 },
    {
      field: 'role',
      headerName: 'Role',
      width: 130,
      renderCell: (params) => (
        <Chip
          label={params.value.replace('_', ' ').toUpperCase()}
          color={getRoleColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'isActive',
      headerName: 'Status',
      width: 100,
      renderCell: (params) => (
        <Chip
          label={params.value ? 'Active' : 'Inactive'}
          color={params.value ? 'success' : 'default'}
          size="small"
        />
      ),
    },
    {
      field: 'lastLogin',
      headerName: 'Last Login',
      width: 150,
      renderCell: (params) =>
        params.value ? new Date(params.value).toLocaleDateString() : 'Never',
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 120,
      getActions: (params) => {
        const actions = [
          <GridActionsCellItem
            icon={<Visibility />}
            label="View"
            onClick={() => handleView(params.row)}
          />,
        ];

        if (canManageAdmin(params.row) && params.row.id !== currentAdmin?.id) {
          actions.push(
            <GridActionsCellItem
              icon={<Edit />}
              label="Edit"
              onClick={() => handleEdit(params.row)}
            />,
            <GridActionsCellItem
              icon={<Delete />}
              label="Delete"
              onClick={() => handleDelete(params.id as string)}
            />
          );
        }

        return actions;
      },
    },
  ];

  // Check if current user has permission to manage admins
  const hasManagePermission = currentAdmin?.permissions?.includes('manage_admins') || 
                              currentAdmin?.role === 'super_admin';

  if (!hasManagePermission) {
    return (
      <Box>
        <Typography variant="h4" gutterBottom>
          Admin Management
        </Typography>
        <Paper sx={{ p: 3 }}>
          <Typography variant="body1" color="error">
            You don't have permission to access this page.
          </Typography>
        </Paper>
      </Box>
    );
  }

  return (
    <Box>
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Typography variant="h4">Admin Management</Typography>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={handleAddNew}
        >
          Add Admin
        </Button>
      </Box>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search admins..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={filteredAdmins}
          columns={columns}
          loading={isLoading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
          getRowId={(row) => row.id}
        />
      </Paper>

      {/* Admin Form Dialog */}
      <AdminForm
        open={isFormOpen}
        onClose={() => setIsFormOpen(false)}
        admin={selectedAdmin}
        onSuccess={() => {
          queryClient.invalidateQueries('admins');
          setIsFormOpen(false);
        }}
      />

      {/* Admin View Dialog */}
      <Dialog
        open={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Admin Details</DialogTitle>
        <DialogContent>
          {selectedAdmin && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Name:</strong> {selectedAdmin.firstName} {selectedAdmin.lastName}
                </Typography>
                <Typography variant="body2">
                  <strong>Email:</strong> {selectedAdmin.email}
                </Typography>
                <Typography variant="body2">
                  <strong>Role:</strong>{' '}
                  <Chip
                    label={selectedAdmin.role.replace('_', ' ').toUpperCase()}
                    color={getRoleColor(selectedAdmin.role) as any}
                    size="small"
                  />
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong>{' '}
                  <Chip
                    label={selectedAdmin.isActive ? 'Active' : 'Inactive'}
                    color={selectedAdmin.isActive ? 'success' : 'default'}
                    size="small"
                  />
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="body2">
                  <strong>Phone:</strong> {selectedAdmin.phoneNumber || 'Not provided'}
                </Typography>
                <Typography variant="body2">
                  <strong>Last Login:</strong>{' '}
                  {selectedAdmin.lastLogin
                    ? new Date(selectedAdmin.lastLogin).toLocaleString()
                    : 'Never'}
                </Typography>
                <Typography variant="body2">
                  <strong>Created:</strong>{' '}
                  {new Date(selectedAdmin.createdAt).toLocaleString()}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="body2">
                  <strong>Permissions:</strong>
                </Typography>
                <Box sx={{ mt: 1 }}>
                  {selectedAdmin.permissions?.map((permission: string) => (
                    <Chip
                      key={permission}
                      label={permission.replace('_', ' ').toUpperCase()}
                      size="small"
                      sx={{ mr: 1, mb: 1 }}
                    />
                  ))}
                </Box>
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsViewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteConfirmOpen}
        onClose={() => setDeleteConfirmOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this admin? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteConfirmOpen(false)}>Cancel</Button>
          <Button
            onClick={confirmDelete}
            color="error"
            disabled={deleteMutation.isLoading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default AdminManagement;

import { Module } from '@nestjs/common';
import { ClientsModule, Transport } from '@nestjs/microservices';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { UniversalProductController } from './controllers/universal-product.controller';
import { UniversalProductService } from './services/universal-product.service';
import { AdminModule } from '../admin/admin.module';
import { SERVICES } from '../../constants/shared';

@Module({
  imports: [
    AdminModule,
    ClientsModule.registerAsync([
      {
        name: SERVICES.PRODUCT,
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('rabbitmq.url')],
            queue: 'product_queue',
            queueOptions: {
              durable: true,
            },
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  controllers: [UniversalProductController],
  providers: [UniversalProductService],
  exports: [UniversalProductService],
})
export class UniversalProductModule {}

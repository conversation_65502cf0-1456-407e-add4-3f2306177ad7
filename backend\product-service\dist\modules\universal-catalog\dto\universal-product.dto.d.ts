import { ProductVariant } from '../../../types/shared';
export declare class CreateUniversalProductDto {
    name: string;
    description: string;
    category: string;
    subcategory: string;
    brand: string;
    suggestedPrice: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isActive?: boolean;
    tags?: string[];
    specifications?: {
        [key: string]: any;
    };
}
export declare class UpdateUniversalProductDto {
    name?: string;
    description?: string;
    category?: string;
    subcategory?: string;
    brand?: string;
    suggestedPrice?: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isActive?: boolean;
    tags?: string[];
    specifications?: {
        [key: string]: any;
    };
}
export declare class CreateMerchantInventoryDto {
    universalProductId: string;
    price: number;
    stock?: number;
    isAvailable?: boolean;
    minStock?: number;
    maxStock?: number;
    location?: string;
}
export declare class UpdateMerchantInventoryDto {
    price?: number;
    stock?: number;
    isAvailable?: boolean;
    minStock?: number;
    maxStock?: number;
    location?: string;
}
export declare class UniversalProductSearchDto {
    search?: string;
    category?: string;
    subcategory?: string;
    brand?: string;
    tags?: string[];
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

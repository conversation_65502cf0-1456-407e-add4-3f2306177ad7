import { Controller, Get, Post, Put, Delete, Body, Param, Query, UseGuards } from '@nestjs/common';
import { UniversalProductService } from '../services/universal-product.service';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
import { JwtAuthGuard } from '../../admin/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../admin/guards/permissions.guard';
import { RequirePermissions } from '../../admin/decorators/permissions.decorator';
import { AdminPermission } from '../../admin/schemas/admin.schema';

@Controller('universal-products')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(AdminPermission.MANAGE_PRODUCTS)
export class UniversalProductController {
  constructor(private readonly universalProductService: UniversalProductService) {}

  @Post()
  async createProduct(@Body() createDto: CreateUniversalProductDto) {
    return this.universalProductService.createProduct(createDto);
  }

  @Get()
  async findAll(@Query() searchDto: UniversalProductSearchDto) {
    return this.universalProductService.findAll(searchDto);
  }

  @Get('categories')
  async getCategories() {
    return this.universalProductService.getCategories();
  }

  @Get('subcategories')
  async getSubcategories(@Query('category') category?: string) {
    return this.universalProductService.getSubcategories(category);
  }

  @Get('brands')
  async getBrands(
    @Query('category') category?: string,
    @Query('subcategory') subcategory?: string,
  ) {
    return this.universalProductService.getBrands(category, subcategory);
  }

  @Get('search/barcode/:barcode')
  async searchByBarcode(@Param('barcode') barcode: string) {
    return this.universalProductService.searchByBarcode(barcode);
  }

  @Get('search/sku/:sku')
  async searchBySku(@Param('sku') sku: string) {
    return this.universalProductService.searchBySku(sku);
  }

  @Get('analytics')
  @RequirePermissions(AdminPermission.VIEW_ANALYTICS)
  async getProductAnalytics() {
    return this.universalProductService.getProductAnalytics();
  }

  @Get(':id')
  async findById(@Param('id') id: string) {
    return this.universalProductService.findById(id);
  }

  @Put(':id')
  async updateProduct(
    @Param('id') id: string,
    @Body() updateDto: UpdateUniversalProductDto,
  ) {
    return this.universalProductService.updateProduct(id, updateDto);
  }

  @Delete(':id')
  async deleteProduct(@Param('id') id: string) {
    return this.universalProductService.deleteProduct(id);
  }

  @Post('bulk-import')
  async bulkImport(@Body() products: CreateUniversalProductDto[]) {
    return this.universalProductService.bulkImport(products);
  }

  @Put(':id/activate')
  async activateProduct(@Param('id') id: string) {
    return this.universalProductService.updateProduct(id, { isActive: true });
  }

  @Put(':id/deactivate')
  async deactivateProduct(@Param('id') id: string) {
    return this.universalProductService.updateProduct(id, { isActive: false });
  }
}

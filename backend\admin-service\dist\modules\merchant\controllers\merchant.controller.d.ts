import { MerchantService } from '../services/merchant.service';
export declare class MerchantController {
    private readonly merchantService;
    constructor(merchantService: MerchantService);
    findAll(page?: number, limit?: number, search?: string, status?: string): Promise<any>;
    getMerchantAnalytics(): Promise<any>;
    findById(id: string): Promise<any>;
    getMerchantInventory(id: string, page?: number, limit?: number): Promise<any>;
    approveMerchant(id: string): Promise<any>;
    rejectMerchant(id: string): Promise<any>;
    suspendMerchant(id: string): Promise<any>;
    activateMerchant(id: string): Promise<any>;
    deleteMerchant(id: string): Promise<any>;
}

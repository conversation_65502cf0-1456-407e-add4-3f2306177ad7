import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { AnalyticsService } from '../services/analytics.service';
import { JwtAuthGuard } from '../../admin/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../admin/guards/permissions.guard';
import { RequirePermissions } from '../../admin/decorators/permissions.decorator';
import { AdminPermission } from '../../admin/schemas/admin.schema';

@Controller('analytics')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(AdminPermission.VIEW_ANALYTICS)
export class AnalyticsController {
  constructor(private readonly analyticsService: AnalyticsService) {}

  @Get('dashboard')
  async getDashboardAnalytics() {
    return this.analyticsService.getDashboardAnalytics();
  }

  @Get('products')
  async getProductAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.analyticsService.getProductAnalytics(startDate, endDate);
  }

  @Get('merchants')
  async getMerchantAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.analyticsService.getMerchantAnalytics(startDate, endDate);
  }

  @Get('sales')
  async getSalesAnalytics(
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
    @Query('groupBy') groupBy?: 'day' | 'week' | 'month',
  ) {
    return this.analyticsService.getSalesAnalytics(startDate, endDate, groupBy);
  }

  @Get('top-products')
  async getTopProducts(
    @Query('limit') limit?: number,
    @Query('period') period?: string,
  ) {
    return this.analyticsService.getTopProducts(limit, period);
  }

  @Get('top-merchants')
  async getTopMerchants(
    @Query('limit') limit?: number,
    @Query('period') period?: string,
  ) {
    return this.analyticsService.getTopMerchants(limit, period);
  }
}

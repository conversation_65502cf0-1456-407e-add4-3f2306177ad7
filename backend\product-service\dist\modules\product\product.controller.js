"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const product_service_1 = require("./product.service");
const shared_lib_1 = require("@kqick/shared-lib");
let ProductController = class ProductController {
    constructor(productService) {
        this.productService = productService;
    }
    create(data) {
        return this.productService.create(data.merchantId, data.product);
    }
    findAll(data) {
        return this.productService.findAll(data.merchantId, data.query);
    }
    findOne(data) {
        return this.productService.findOne(data.merchantId, data.id);
    }
    update(data) {
        return this.productService.update(data.merchantId, data.id, data.product);
    }
    updateInventory(data) {
        return this.productService.updateInventory(data.merchantId, data.id, data.inventory);
    }
    remove(data) {
        return this.productService.remove(data.merchantId, data.id);
    }
    getCategories(data) {
        return this.productService.getCategories(data.merchantId);
    }
};
exports.ProductController = ProductController;
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.PRODUCT_CREATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "create", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_all_products'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "findAll", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_one_product'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "findOne", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.PRODUCT_UPDATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "update", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.INVENTORY_UPDATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "updateInventory", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.PRODUCT_DELETED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "remove", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_product_categories'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], ProductController.prototype, "getCategories", null);
exports.ProductController = ProductController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [product_service_1.ProductService])
], ProductController);
//# sourceMappingURL=product.controller.js.map
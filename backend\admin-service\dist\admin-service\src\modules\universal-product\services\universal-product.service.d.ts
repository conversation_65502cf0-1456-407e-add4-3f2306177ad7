import { ClientProxy } from '@nestjs/microservices';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
export declare class UniversalProductService {
    private readonly productClient;
    constructor(productClient: ClientProxy);
    createProduct(createDto: CreateUniversalProductDto): Promise<any>;
    findAll(searchDto: UniversalProductSearchDto): Promise<any>;
    findById(id: string): Promise<any>;
    updateProduct(id: string, updateDto: UpdateUniversalProductDto): Promise<any>;
    deleteProduct(id: string): Promise<any>;
    getCategories(): Promise<any>;
    getSubcategories(category?: string): Promise<any>;
    getBrands(category?: string, subcategory?: string): Promise<any>;
    searchByBarcode(barcode: string): Promise<any>;
    searchBySku(sku: string): Promise<any>;
    bulkImport(products: CreateUniversalProductDto[]): Promise<{
        total: number;
        successful: number;
        failed: number;
        results: any[];
    }>;
    getProductAnalytics(): Promise<{
        totalProducts: any;
        totalCategories: any;
    }>;
}

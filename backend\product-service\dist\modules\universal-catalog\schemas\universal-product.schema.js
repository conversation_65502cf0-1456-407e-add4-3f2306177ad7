"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalProductSchema = exports.UniversalProduct = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let UniversalProduct = class UniversalProduct extends mongoose_2.Document {
};
exports.UniversalProduct = UniversalProduct;
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "name", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "description", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "category", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "subcategory", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "brand", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], UniversalProduct.prototype, "suggestedPrice", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], UniversalProduct.prototype, "imageUrl", void 0);
__decorate([
    (0, mongoose_1.Prop)({ unique: true, sparse: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "barcode", void 0);
__decorate([
    (0, mongoose_1.Prop)({ unique: true, sparse: true }),
    __metadata("design:type", String)
], UniversalProduct.prototype, "sku", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.Mixed }),
    __metadata("design:type", Object)
], UniversalProduct.prototype, "variants", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], UniversalProduct.prototype, "isActive", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: [String], default: [] }),
    __metadata("design:type", Array)
], UniversalProduct.prototype, "tags", void 0);
__decorate([
    (0, mongoose_1.Prop)({ type: mongoose_2.Schema.Types.Mixed }),
    __metadata("design:type", Object)
], UniversalProduct.prototype, "specifications", void 0);
exports.UniversalProduct = UniversalProduct = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], UniversalProduct);
exports.UniversalProductSchema = mongoose_1.SchemaFactory.createForClass(UniversalProduct);
exports.UniversalProductSchema.index({ name: 'text', description: 'text', brand: 'text', tags: 'text' });
exports.UniversalProductSchema.index({ category: 1, subcategory: 1 });
exports.UniversalProductSchema.index({ brand: 1 });
exports.UniversalProductSchema.index({ barcode: 1 });
exports.UniversalProductSchema.index({ sku: 1 });
exports.UniversalProductSchema.index({ isActive: 1 });
//# sourceMappingURL=universal-product.schema.js.map
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ProductVariant } from '@kqick/shared-lib';
export declare class UniversalProduct extends Document {
    name: string;
    description: string;
    category: string;
    subcategory: string;
    brand: string;
    suggestedPrice: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isActive: boolean;
    tags: string[];
    specifications?: {
        [key: string]: any;
    };
}
export declare const UniversalProductSchema: MongooseSchema<UniversalProduct, import("mongoose").Model<UniversalProduct, any, any, any, Document<unknown, any, UniversalProduct, any> & UniversalProduct & Required<{
    _id: unknown;
}> & {
    __v: number;
}, any>, {}, {}, {}, {}, import("mongoose").DefaultSchemaOptions, UniversalProduct, Document<unknown, {}, import("mongoose").FlatRecord<UniversalProduct>, {}> & import("mongoose").FlatRecord<UniversalProduct> & Required<{
    _id: unknown;
}> & {
    __v: number;
}>;

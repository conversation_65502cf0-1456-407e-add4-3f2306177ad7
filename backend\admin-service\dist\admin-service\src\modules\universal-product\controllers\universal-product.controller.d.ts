import { UniversalProductService } from '../services/universal-product.service';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
export declare class UniversalProductController {
    private readonly universalProductService;
    constructor(universalProductService: UniversalProductService);
    createProduct(createDto: CreateUniversalProductDto): Promise<any>;
    findAll(searchDto: UniversalProductSearchDto): Promise<any>;
    getCategories(): Promise<any>;
    getSubcategories(category?: string): Promise<any>;
    getBrands(category?: string, subcategory?: string): Promise<any>;
    searchByBarcode(barcode: string): Promise<any>;
    searchBySku(sku: string): Promise<any>;
    getProductAnalytics(): Promise<{
        totalProducts: any;
        totalCategories: any;
    }>;
    findById(id: string): Promise<any>;
    updateProduct(id: string, updateDto: UpdateUniversalProductDto): Promise<any>;
    deleteProduct(id: string): Promise<any>;
    bulkImport(products: CreateUniversalProductDto[]): Promise<{
        total: number;
        successful: number;
        failed: number;
        results: any[];
    }>;
    activateProduct(id: string): Promise<any>;
    deactivateProduct(id: string): Promise<any>;
}

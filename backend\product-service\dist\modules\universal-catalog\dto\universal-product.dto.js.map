{"version": 3, "file": "universal-product.dto.js", "sourceRoot": "", "sources": ["../../../../src/modules/universal-catalog/dto/universal-product.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAqG;AACrG,yDAAyC;AACzC,kDAAuD;AAEvD,MAAa,yBAAyB;CA+CrC;AA/CD,8DA+CC;AA7CC;IADC,IAAA,0BAAQ,GAAE;;uDACE;AAGb;IADC,IAAA,0BAAQ,GAAE;;8DACS;AAGpB;IADC,IAAA,0BAAQ,GAAE;;2DACM;AAGjB;IADC,IAAA,0BAAQ,GAAE;;8DACS;AAGpB;IADC,IAAA,0BAAQ,GAAE;;wDACG;AAGd;IADC,IAAA,0BAAQ,GAAE;;iEACY;AAIvB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACE;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,uBAAc,CAAC;;2DACkB;AAI7C;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;2DACO;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAGhB;IADC,IAAA,4BAAU,GAAE;;iEAC2B;AAG1C,MAAa,yBAAyB;CAqDrC;AArDD,8DAqDC;AAlDC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACE;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,uBAAc,CAAC;;2DACkB;AAI7C;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;2DACO;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAGhB;IADC,IAAA,4BAAU,GAAE;;iEAC2B;AAG1C,MAAa,0BAA0B;CA0BtC;AA1BD,gEA0BC;AAxBC;IADC,IAAA,0BAAQ,GAAE;;sEACgB;AAG3B;IADC,IAAA,0BAAQ,GAAE;;yDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;+DACU;AAItB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAGpB,MAAa,0BAA0B;CAwBtC;AAxBD,gEAwBC;AArBC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;+DACU;AAItB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACO;AAGpB,MAAa,yBAAyB;CA6CrC;AA7CD,8DA6CC;AA1CC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACgB"}
{"version": 3, "file": "universal-product.service.js", "sourceRoot": "", "sources": ["../../../../src/modules/universal-product/services/universal-product.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yDAAoD;AACpD,+BAAsC;AAEtC,sDAA6D;AAGtD,IAAM,uBAAuB,GAA7B,MAAM,uBAAuB;IAClC,YAC6C,aAA0B;QAA1B,kBAAa,GAAb,aAAa,CAAa;IACpE,CAAC;IAEJ,KAAK,CAAC,aAAa,CAAC,SAAoC;QACtD,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAM,CAAC,yBAAyB,EAAE,SAAS,CAAC,CACrE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,OAAO,CAAC,SAAoC;QAChD,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,6BAA6B,EAAE,SAAS,CAAC,CAClE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,QAAQ,CAAC,EAAU;QACvB,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,EAAE,EAAE,CAAC,CAChE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU,EAAE,SAAoC;QAClE,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAM,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,SAAS,EAAE,CAAC,CAC7E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa,CAAC,EAAU;QAC5B,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,eAAM,CAAC,yBAAyB,EAAE,EAAE,EAAE,EAAE,CAAC,CAClE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,aAAa;QACjB,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAChE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,gBAAgB,CAAC,QAAiB;QACtC,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,QAAQ,EAAE,CAAC,CAC7E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,SAAS,CAAC,QAAiB,EAAE,WAAoB;QACrD,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,QAAQ,EAAE,WAAW,EAAE,CAAC,CACnF,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,OAAe;QACnC,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,OAAO,EAAE,CAAC,CAC5E,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,GAAW;QAC3B,OAAO,IAAA,qBAAc,EACnB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,GAAG,EAAE,CAAC,CACpE,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,UAAU,CAAC,QAAqC;QACpD,MAAM,OAAO,GAAG,EAAE,CAAC;QACnB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC;gBACjD,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,CAAC,CAAC;YACnD,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,OAAO,CAAC,IAAI,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,OAAO,EAAE,OAAO,EAAE,CAAC,CAAC;YAClE,CAAC;QACH,CAAC;QACD,OAAO;YACL,KAAK,EAAE,QAAQ,CAAC,MAAM;YACtB,UAAU,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;YACjD,MAAM,EAAE,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,MAAM;YAC9C,OAAO;SACR,CAAC;IACJ,CAAC;IAED,KAAK,CAAC,mBAAmB;QAEvB,MAAM,CAAC,UAAU,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YACpD,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,CAAC,OAAO,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC;SACpC,CAAC,CAAC;QAEH,OAAO;YACL,aAAa,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC;YACvC,eAAe,EAAE,UAAU,CAAC,MAAM;SAEnC,CAAC;IACJ,CAAC;CACF,CAAA;AAhGY,0DAAuB;kCAAvB,uBAAuB;IADnC,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,iBAAQ,CAAC,OAAO,CAAC,CAAA;qCAAiC,2BAAW;GAF5D,uBAAuB,CAgGnC"}
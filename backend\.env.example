# MongoDB Configuration
MONGODB_URI=mongodb+srv://lesibamosese03:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0

# RabbitMQ Configuration
RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672

# Redis Configuration
REDIS_URL=redis://:kqickdev@redis:6379

# JWT Configuration
JWT_SECRET=kqick-super-secret-jwt-key-change-in-production
JWT_EXPIRES_IN=24h

# Service Ports
API_GATEWAY_PORT=3000
AUTH_SERVICE_PORT=3001
PRODUCT_SERVICE_PORT=3002
MERCHANT_SERVICE_PORT=3003
ADMIN_SERVICE_PORT=3004

# Environment
NODE_ENV=development

import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { MerchantInventoryService } from '../services/merchant-inventory.service';
import {
  CreateMerchantInventoryDto,
  UpdateMerchantInventoryDto,
} from '../dto/universal-product.dto';
import { TOPICS } from '@kqick/shared-lib';

@Controller()
export class MerchantInventoryController {
  constructor(
    private readonly merchantInventoryService: MerchantInventoryService,
  ) {}

  @MessagePattern(TOPICS.MERCHANT_INVENTORY_CREATED)
  addProductToInventory(
    @Payload()
    data: { merchantId: string; createDto: CreateMerchantInventoryDto },
  ) {
    return this.merchantInventoryService.addProductToInventory(
      data.merchantId,
      data.createDto,
    );
  }

  @MessagePattern('get_merchant_inventory')
  getMerchantInventory(
    @Payload()
    data: {
      merchantId: string;
      page?: number;
      limit?: number;
      search?: string;
      category?: string;
    },
  ) {
    return this.merchantInventoryService.getMerchantInventory(
      data.merchantId,
      data.page,
      data.limit,
      data.search,
      data.category,
    );
  }

  @MessagePattern(TOPICS.MERCHANT_INVENTORY_UPDATED)
  updateMerchantInventory(
    @Payload()
    data: {
      merchantId: string;
      inventoryId: string;
      updateDto: UpdateMerchantInventoryDto;
    },
  ) {
    return this.merchantInventoryService.updateMerchantInventory(
      data.merchantId,
      data.inventoryId,
      data.updateDto,
    );
  }

  @MessagePattern(TOPICS.MERCHANT_INVENTORY_DELETED)
  removeMerchantInventory(
    @Payload() data: { merchantId: string; inventoryId: string },
  ) {
    return this.merchantInventoryService.removeMerchantInventory(
      data.merchantId,
      data.inventoryId,
    );
  }

  @MessagePattern('get_merchant_inventory_by_product')
  getMerchantInventoryByProduct(
    @Payload() data: { merchantId: string; universalProductId: string },
  ) {
    return this.merchantInventoryService.getMerchantInventoryByProduct(
      data.merchantId,
      data.universalProductId,
    );
  }

  @MessagePattern('update_merchant_stock')
  updateStock(
    @Payload()
    data: {
      merchantId: string;
      universalProductId: string;
      stockChange: number;
    },
  ) {
    return this.merchantInventoryService.updateStock(
      data.merchantId,
      data.universalProductId,
      data.stockChange,
    );
  }

  @MessagePattern('get_low_stock_items')
  getLowStockItems(@Payload() data: { merchantId: string }) {
    return this.merchantInventoryService.getLowStockItems(data.merchantId);
  }
}

import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Grid,
  Chip,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
} from '@mui/material';
import {
  Search,
  CheckCircle,
  Cancel,
  Pause,
  PlayArrow,
} from '@mui/icons-material';
import { DataGrid, GridColDef, GridActionsCellItem } from '@mui/x-data-grid';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { apiService } from '../../services/api';

const Merchants: React.FC = () => {
  const queryClient = useQueryClient();
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedMerchant, setSelectedMerchant] = useState<any>(null);
  const [isViewOpen, setIsViewOpen] = useState(false);

  const { data: merchants, isLoading } = useQuery(
    ['merchants', searchTerm],
    () => apiService.getMerchants({ search: searchTerm }),
    {
      keepPreviousData: true,
    }
  );

  const approveMutation = useMutation(apiService.approveMerchant, {
    onSuccess: () => queryClient.invalidateQueries('merchants'),
  });

  const rejectMutation = useMutation(apiService.rejectMerchant, {
    onSuccess: () => queryClient.invalidateQueries('merchants'),
  });

  const suspendMutation = useMutation(apiService.suspendMerchant, {
    onSuccess: () => queryClient.invalidateQueries('merchants'),
  });

  const activateMutation = useMutation(apiService.activateMerchant, {
    onSuccess: () => queryClient.invalidateQueries('merchants'),
  });

  const handleView = (merchant: any) => {
    setSelectedMerchant(merchant);
    setIsViewOpen(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'success';
      case 'pending':
        return 'warning';
      case 'rejected':
        return 'error';
      case 'suspended':
        return 'default';
      default:
        return 'default';
    }
  };

  const columns: GridColDef[] = [
    { field: 'businessName', headerName: 'Business Name', width: 200 },
    { field: 'ownerName', headerName: 'Owner Name', width: 150 },
    { field: 'email', headerName: 'Email', width: 200 },
    { field: 'phoneNumber', headerName: 'Phone', width: 130 },
    {
      field: 'status',
      headerName: 'Status',
      width: 120,
      renderCell: (params) => (
        <Chip
          label={params.value}
          color={getStatusColor(params.value) as any}
          size="small"
        />
      ),
    },
    {
      field: 'createdAt',
      headerName: 'Registered',
      width: 120,
      renderCell: (params) =>
        new Date(params.value).toLocaleDateString(),
    },
    {
      field: 'actions',
      type: 'actions',
      headerName: 'Actions',
      width: 200,
      getActions: (params) => {
        const actions = [
          <GridActionsCellItem
            icon={<Search />}
            label="View"
            onClick={() => handleView(params.row)}
          />,
        ];

        if (params.row.status === 'pending') {
          actions.push(
            <GridActionsCellItem
              icon={<CheckCircle />}
              label="Approve"
              onClick={() => approveMutation.mutate(params.id as string)}
            />,
            <GridActionsCellItem
              icon={<Cancel />}
              label="Reject"
              onClick={() => rejectMutation.mutate(params.id as string)}
            />
          );
        }

        if (params.row.status === 'active') {
          actions.push(
            <GridActionsCellItem
              icon={<Pause />}
              label="Suspend"
              onClick={() => suspendMutation.mutate(params.id as string)}
            />
          );
        }

        if (params.row.status === 'suspended') {
          actions.push(
            <GridActionsCellItem
              icon={<PlayArrow />}
              label="Activate"
              onClick={() => activateMutation.mutate(params.id as string)}
            />
          );
        }

        return actions;
      },
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Merchants
      </Typography>

      <Paper sx={{ p: 2, mb: 3 }}>
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              placeholder="Search merchants..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <Search sx={{ mr: 1, color: 'text.secondary' }} />,
              }}
            />
          </Grid>
        </Grid>
      </Paper>

      <Paper sx={{ height: 600, width: '100%' }}>
        <DataGrid
          rows={merchants?.data || []}
          columns={columns}
          loading={isLoading}
          pageSizeOptions={[25, 50, 100]}
          initialState={{
            pagination: {
              paginationModel: { pageSize: 25 },
            },
          }}
          disableRowSelectionOnClick
          getRowId={(row) => row.id || row._id}
        />
      </Paper>

      {/* Merchant View Dialog */}
      <Dialog
        open={isViewOpen}
        onClose={() => setIsViewOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>Merchant Details</DialogTitle>
        <DialogContent>
          {selectedMerchant && (
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Business Information
                </Typography>
                <Typography variant="body2">
                  <strong>Business Name:</strong> {selectedMerchant.businessName}
                </Typography>
                <Typography variant="body2">
                  <strong>Business Type:</strong> {selectedMerchant.businessType}
                </Typography>
                <Typography variant="body2">
                  <strong>Registration Number:</strong> {selectedMerchant.registrationNumber}
                </Typography>
                <Typography variant="body2">
                  <strong>Address:</strong> {selectedMerchant.address}
                </Typography>
              </Grid>
              <Grid item xs={12} md={6}>
                <Typography variant="h6" gutterBottom>
                  Owner Information
                </Typography>
                <Typography variant="body2">
                  <strong>Owner Name:</strong> {selectedMerchant.ownerName}
                </Typography>
                <Typography variant="body2">
                  <strong>Email:</strong> {selectedMerchant.email}
                </Typography>
                <Typography variant="body2">
                  <strong>Phone:</strong> {selectedMerchant.phoneNumber}
                </Typography>
                <Typography variant="body2">
                  <strong>ID Number:</strong> {selectedMerchant.idNumber}
                </Typography>
              </Grid>
              <Grid item xs={12}>
                <Typography variant="h6" gutterBottom>
                  Status & Dates
                </Typography>
                <Typography variant="body2">
                  <strong>Status:</strong>{' '}
                  <Chip
                    label={selectedMerchant.status}
                    color={getStatusColor(selectedMerchant.status) as any}
                    size="small"
                  />
                </Typography>
                <Typography variant="body2">
                  <strong>Registered:</strong>{' '}
                  {new Date(selectedMerchant.createdAt).toLocaleDateString()}
                </Typography>
                {selectedMerchant.approvedAt && (
                  <Typography variant="body2">
                    <strong>Approved:</strong>{' '}
                    {new Date(selectedMerchant.approvedAt).toLocaleDateString()}
                  </Typography>
                )}
              </Grid>
            </Grid>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setIsViewOpen(false)}>Close</Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default Merchants;

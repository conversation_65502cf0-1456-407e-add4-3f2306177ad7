import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { UniversalProduct } from '../schemas/universal-product.schema';
import { MerchantInventory } from '../schemas/merchant-inventory.schema';
import {
  CreateUniversalProductDto,
  UpdateUniversalProductDto,
  UniversalProductSearchDto,
} from '../dto/universal-product.dto';
import { NotFoundError, PaginatedResponse } from '../../../types/shared';

@Injectable()
export class UniversalCatalogService {
  constructor(
    @InjectModel(UniversalProduct.name)
    private readonly universalProductModel: Model<UniversalProduct>,
    @InjectModel(MerchantInventory.name)
    private readonly merchantInventoryModel: Model<MerchantInventory>,
  ) {}

  async createUniversalProduct(
    createDto: CreateUniversalProductDto,
  ): Promise<UniversalProduct> {
    const product = new this.universalProductModel(createDto);
    return product.save();
  }

  async findAllUniversalProducts(
    searchDto: UniversalProductSearchDto,
  ): Promise<PaginatedResponse<UniversalProduct>> {
    const {
      search,
      category,
      subcategory,
      brand,
      tags,
      minPrice,
      maxPrice,
      page = 1,
      limit = 20,
      sortBy = 'createdAt',
      sortOrder = 'desc',
    } = searchDto;

    const query: any = { isActive: true };

    // Text search
    if (search) {
      query.$text = { $search: search };
    }

    // Category filters
    if (category) {
      query.category = category;
    }
    if (subcategory) {
      query.subcategory = subcategory;
    }
    if (brand) {
      query.brand = new RegExp(brand, 'i');
    }

    // Tags filter
    if (tags && tags.length > 0) {
      query.tags = { $in: tags };
    }

    // Price range filter
    if (minPrice !== undefined || maxPrice !== undefined) {
      query.suggestedPrice = {};
      if (minPrice !== undefined) {
        query.suggestedPrice.$gte = minPrice;
      }
      if (maxPrice !== undefined) {
        query.suggestedPrice.$lte = maxPrice;
      }
    }

    const skip = (page - 1) * limit;
    const sortOptions: any = {};
    sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;

    const [products, total] = await Promise.all([
      this.universalProductModel
        .find(query)
        .sort(sortOptions)
        .skip(skip)
        .limit(limit)
        .exec(),
      this.universalProductModel.countDocuments(query),
    ]);

    return {
      data: products,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findUniversalProductById(id: string): Promise<UniversalProduct> {
    const product = await this.universalProductModel.findById(id);
    if (!product) {
      throw new NotFoundError('Universal product not found');
    }
    return product;
  }

  async updateUniversalProduct(
    id: string,
    updateDto: UpdateUniversalProductDto,
  ): Promise<UniversalProduct> {
    const product = await this.universalProductModel.findByIdAndUpdate(
      id,
      updateDto,
      { new: true },
    );
    if (!product) {
      throw new NotFoundError('Universal product not found');
    }
    return product;
  }

  async deleteUniversalProduct(id: string): Promise<void> {
    const result = await this.universalProductModel.findByIdAndDelete(id);
    if (!result) {
      throw new NotFoundError('Universal product not found');
    }
  }

  async getCategories(): Promise<string[]> {
    const categories = await this.universalProductModel.distinct('category', {
      isActive: true,
    });
    return categories.sort();
  }

  async getSubcategories(category?: string): Promise<string[]> {
    const query: any = { isActive: true };
    if (category) {
      query.category = category;
    }
    const subcategories = await this.universalProductModel.distinct(
      'subcategory',
      query,
    );
    return subcategories.sort();
  }

  async getBrands(category?: string, subcategory?: string): Promise<string[]> {
    const query: any = { isActive: true };
    if (category) {
      query.category = category;
    }
    if (subcategory) {
      query.subcategory = subcategory;
    }
    const brands = await this.universalProductModel.distinct('brand', query);
    return brands.sort();
  }

  async searchByBarcode(barcode: string): Promise<UniversalProduct | null> {
    return this.universalProductModel.findOne({ barcode, isActive: true });
  }

  async searchBySku(sku: string): Promise<UniversalProduct | null> {
    return this.universalProductModel.findOne({ sku, isActive: true });
  }
}

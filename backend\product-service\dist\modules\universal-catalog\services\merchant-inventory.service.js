"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantInventoryService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const merchant_inventory_schema_1 = require("../schemas/merchant-inventory.schema");
const universal_product_schema_1 = require("../schemas/universal-product.schema");
const shared_1 = require("../../../types/shared");
let MerchantInventoryService = class MerchantInventoryService {
    constructor(merchantInventoryModel, universalProductModel) {
        this.merchantInventoryModel = merchantInventoryModel;
        this.universalProductModel = universalProductModel;
    }
    async addProductToInventory(merchantId, createDto) {
        const universalProduct = await this.universalProductModel.findById(createDto.universalProductId);
        if (!universalProduct) {
            throw new shared_1.NotFoundError('Universal product not found');
        }
        const existingInventory = await this.merchantInventoryModel.findOne({
            merchantId,
            universalProductId: createDto.universalProductId,
        });
        if (existingInventory) {
            throw new Error('Product already exists in merchant inventory');
        }
        const inventory = new this.merchantInventoryModel(Object.assign(Object.assign({}, createDto), { merchantId }));
        return inventory.save();
    }
    async getMerchantInventory(merchantId, page = 1, limit = 20, search, category) {
        var _a;
        const skip = (page - 1) * limit;
        const pipeline = [
            { $match: { merchantId } },
            {
                $lookup: {
                    from: 'universalproducts',
                    localField: 'universalProductId',
                    foreignField: '_id',
                    as: 'product',
                },
            },
            { $unwind: '$product' },
            { $match: { 'product.isActive': true } },
        ];
        if (search) {
            pipeline.push({
                $match: {
                    $or: [
                        { 'product.name': { $regex: search, $options: 'i' } },
                        { 'product.description': { $regex: search, $options: 'i' } },
                        { 'product.brand': { $regex: search, $options: 'i' } },
                    ],
                },
            });
        }
        if (category) {
            pipeline.push({
                $match: { 'product.category': category },
            });
        }
        const countPipeline = [...pipeline, { $count: 'total' }];
        pipeline.push({ $skip: skip }, { $limit: limit });
        pipeline.push({
            $project: {
                _id: 1,
                merchantId: 1,
                price: 1,
                stock: 1,
                isAvailable: 1,
                minStock: 1,
                maxStock: 1,
                location: 1,
                createdAt: 1,
                updatedAt: 1,
                product: {
                    _id: '$product._id',
                    name: '$product.name',
                    description: '$product.description',
                    category: '$product.category',
                    subcategory: '$product.subcategory',
                    brand: '$product.brand',
                    suggestedPrice: '$product.suggestedPrice',
                    imageUrl: '$product.imageUrl',
                    barcode: '$product.barcode',
                    sku: '$product.sku',
                    variants: '$product.variants',
                    tags: '$product.tags',
                    specifications: '$product.specifications',
                },
            },
        });
        const [inventoryItems, totalResult] = await Promise.all([
            this.merchantInventoryModel.aggregate(pipeline),
            this.merchantInventoryModel.aggregate(countPipeline),
        ]);
        const total = ((_a = totalResult[0]) === null || _a === void 0 ? void 0 : _a.total) || 0;
        return {
            data: inventoryItems,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async updateMerchantInventory(merchantId, inventoryId, updateDto) {
        const inventory = await this.merchantInventoryModel.findOneAndUpdate({ _id: inventoryId, merchantId }, updateDto, { new: true });
        if (!inventory) {
            throw new shared_1.NotFoundError('Inventory item not found');
        }
        return inventory;
    }
    async removeMerchantInventory(merchantId, inventoryId) {
        const result = await this.merchantInventoryModel.findOneAndDelete({
            _id: inventoryId,
            merchantId,
        });
        if (!result) {
            throw new shared_1.NotFoundError('Inventory item not found');
        }
    }
    async getMerchantInventoryByProduct(merchantId, universalProductId) {
        return this.merchantInventoryModel.findOne({
            merchantId,
            universalProductId,
        });
    }
    async updateStock(merchantId, universalProductId, stockChange) {
        const inventory = await this.merchantInventoryModel.findOne({
            merchantId,
            universalProductId,
        });
        if (!inventory) {
            throw new shared_1.NotFoundError('Inventory item not found');
        }
        inventory.stock += stockChange;
        if (inventory.stock < 0) {
            inventory.stock = 0;
        }
        return inventory.save();
    }
    async getLowStockItems(merchantId) {
        return this.merchantInventoryModel
            .find({
            merchantId,
            $expr: {
                $and: [
                    { $ne: ['$minStock', null] },
                    { $lte: ['$stock', '$minStock'] },
                ],
            },
        })
            .populate('universalProductId');
    }
};
exports.MerchantInventoryService = MerchantInventoryService;
exports.MerchantInventoryService = MerchantInventoryService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(merchant_inventory_schema_1.MerchantInventory.name)),
    __param(1, (0, mongoose_1.InjectModel)(universal_product_schema_1.UniversalProduct.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], MerchantInventoryService);
//# sourceMappingURL=merchant-inventory.service.js.map
import 'package:equatable/equatable.dart';
import 'package:json_annotation/json_annotation.dart';

part 'universal_product.g.dart';

@JsonSerializable()
class UniversalProduct extends Equatable {
  final String id;
  final String name;
  final String description;
  final String category;
  final String subcategory;
  final String brand;
  final double suggestedPrice;
  final String? imageUrl;
  final String? barcode;
  final String? sku;
  final Map<String, dynamic>? variants;
  final bool isActive;
  final List<String> tags;
  final Map<String, dynamic>? specifications;
  final DateTime createdAt;
  final DateTime updatedAt;

  const UniversalProduct({
    required this.id,
    required this.name,
    required this.description,
    required this.category,
    required this.subcategory,
    required this.brand,
    required this.suggestedPrice,
    this.imageUrl,
    this.barcode,
    this.sku,
    this.variants,
    this.isActive = true,
    this.tags = const [],
    this.specifications,
    required this.createdAt,
    required this.updatedAt,
  });

  factory UniversalProduct.fromJson(Map<String, dynamic> json) =>
      _$UniversalProductFromJson(json);
  Map<String, dynamic> toJson() => _$UniversalProductToJson(this);

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        category,
        subcategory,
        brand,
        suggestedPrice,
        imageUrl,
        barcode,
        sku,
        variants,
        isActive,
        tags,
        specifications,
        createdAt,
        updatedAt,
      ];

  UniversalProduct copyWith({
    String? id,
    String? name,
    String? description,
    String? category,
    String? subcategory,
    String? brand,
    double? suggestedPrice,
    String? imageUrl,
    String? barcode,
    String? sku,
    Map<String, dynamic>? variants,
    bool? isActive,
    List<String>? tags,
    Map<String, dynamic>? specifications,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return UniversalProduct(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      brand: brand ?? this.brand,
      suggestedPrice: suggestedPrice ?? this.suggestedPrice,
      imageUrl: imageUrl ?? this.imageUrl,
      barcode: barcode ?? this.barcode,
      sku: sku ?? this.sku,
      variants: variants ?? this.variants,
      isActive: isActive ?? this.isActive,
      tags: tags ?? this.tags,
      specifications: specifications ?? this.specifications,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

@JsonSerializable()
class MerchantInventory extends Equatable {
  final String id;
  final String merchantId;
  final String universalProductId;
  final double price;
  final int stock;
  final bool isAvailable;
  final int? minStock;
  final int? maxStock;
  final String? location;
  final DateTime createdAt;
  final DateTime updatedAt;

  const MerchantInventory({
    required this.id,
    required this.merchantId,
    required this.universalProductId,
    required this.price,
    required this.stock,
    this.isAvailable = true,
    this.minStock,
    this.maxStock,
    this.location,
    required this.createdAt,
    required this.updatedAt,
  });

  factory MerchantInventory.fromJson(Map<String, dynamic> json) =>
      _$MerchantInventoryFromJson(json);
  Map<String, dynamic> toJson() => _$MerchantInventoryToJson(this);

  @override
  List<Object?> get props => [
        id,
        merchantId,
        universalProductId,
        price,
        stock,
        isAvailable,
        minStock,
        maxStock,
        location,
        createdAt,
        updatedAt,
      ];

  MerchantInventory copyWith({
    String? id,
    String? merchantId,
    String? universalProductId,
    double? price,
    int? stock,
    bool? isAvailable,
    int? minStock,
    int? maxStock,
    String? location,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return MerchantInventory(
      id: id ?? this.id,
      merchantId: merchantId ?? this.merchantId,
      universalProductId: universalProductId ?? this.universalProductId,
      price: price ?? this.price,
      stock: stock ?? this.stock,
      isAvailable: isAvailable ?? this.isAvailable,
      minStock: minStock ?? this.minStock,
      maxStock: maxStock ?? this.maxStock,
      location: location ?? this.location,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isLowStock => minStock != null && stock <= minStock!;
  bool get isOutOfStock => stock == 0;
}

@JsonSerializable()
class MerchantInventoryWithProduct extends Equatable {
  final MerchantInventory inventory;
  final UniversalProduct product;

  const MerchantInventoryWithProduct({
    required this.inventory,
    required this.product,
  });

  factory MerchantInventoryWithProduct.fromJson(Map<String, dynamic> json) =>
      _$MerchantInventoryWithProductFromJson(json);
  Map<String, dynamic> toJson() => _$MerchantInventoryWithProductToJson(this);

  @override
  List<Object?> get props => [inventory, product];
}

import 'package:flutter/material.dart';
import 'package:kqick_merchant/core/theme/app_colors.dart';

class UniversalProductSearchBar extends StatefulWidget {
  final Function(String) onSearchChanged;
  final Function(String) onBarcodeScanned;

  const UniversalProductSearchBar({
    super.key,
    required this.onSearchChanged,
    required this.onBarcodeScanned,
  });

  @override
  State<UniversalProductSearchBar> createState() =>
      _UniversalProductSearchBarState();
}

class _UniversalProductSearchBarState extends State<UniversalProductSearchBar> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearching = false;

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _onSearchChanged(String value) {
    setState(() => _isSearching = value.isNotEmpty);
    widget.onSearchChanged(value);
  }

  void _clearSearch() {
    _searchController.clear();
    setState(() => _isSearching = false);
    widget.onSearchChanged('');
  }

  void _scanBarcode() {
    // TODO: Implement barcode scanning
    // For now, show a dialog to manually enter barcode
    showDialog(
      context: context,
      builder:
          (context) =>
              _BarcodeInputDialog(onBarcodeEntered: widget.onBarcodeScanned),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _searchController,
              onChanged: _onSearchChanged,
              decoration: InputDecoration(
                hintText: 'Search products by name, brand, or description...',
                prefixIcon: const Icon(Icons.search, color: AppColors.primary),
                suffixIcon:
                    _isSearching
                        ? IconButton(
                          icon: const Icon(Icons.clear),
                          onPressed: _clearSearch,
                        )
                        : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: BorderSide(color: Colors.grey.shade300),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                  borderSide: const BorderSide(color: AppColors.primary),
                ),
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 12,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Container(
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.qr_code_scanner, color: Colors.white),
              onPressed: _scanBarcode,
              tooltip: 'Scan Barcode',
            ),
          ),
        ],
      ),
    );
  }
}

class _BarcodeInputDialog extends StatefulWidget {
  final Function(String) onBarcodeEntered;

  const _BarcodeInputDialog({required this.onBarcodeEntered});

  @override
  State<_BarcodeInputDialog> createState() => _BarcodeInputDialogState();
}

class _BarcodeInputDialogState extends State<_BarcodeInputDialog> {
  final TextEditingController _barcodeController = TextEditingController();

  @override
  void dispose() {
    _barcodeController.dispose();
    super.dispose();
  }

  void _submitBarcode() {
    final barcode = _barcodeController.text.trim();
    if (barcode.isNotEmpty) {
      widget.onBarcodeEntered(barcode);
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Enter Barcode'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          const Text('Enter the product barcode manually:'),
          const SizedBox(height: 16),
          TextField(
            controller: _barcodeController,
            decoration: const InputDecoration(
              labelText: 'Barcode',
              border: OutlineInputBorder(),
            ),
            keyboardType: TextInputType.number,
            autofocus: true,
            onSubmitted: (_) => _submitBarcode(),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _submitBarcode,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primary,
            foregroundColor: Colors.white,
          ),
          child: const Text('Search'),
        ),
      ],
    );
  }
}

# 🐳 KQICK Docker Build Cloud Setup

This guide will help you set up Docker Build Cloud for the KQICK microservices project.

## 📋 Prerequisites

1. **Docker Desktop** installed with Buildx support
2. **Docker Hub account** with access to Docker Build Cloud
3. **Authentication** to Docker Hub

## 🚀 Quick Start

### 1. Authenticate with Docker Hub
```bash
docker login
```

### 2. Create Cloud Builder
```bash
# Your original command - creates a cloud builder
docker buildx create --driver cloud kqick00/kqick --name kqick-cloud-builder

# Use the cloud builder
docker buildx use kqick-cloud-builder
```

### 3. Build Services

#### Option A: Use the automated script (Recommended)
```bash
# For Linux/Mac
chmod +x docker-build-cloud.sh
./docker-build-cloud.sh

# For Windows PowerShell
.\docker-build-cloud.ps1
```

#### Option B: Manual build commands
```bash
# Build each service individually
docker buildx build --platform linux/amd64,linux/arm64 \
  --tag kqick00/kqick-product-service:latest \
  --push \
  ./product-service

docker buildx build --platform linux/amd64,linux/arm64 \
  --tag kqick00/kqick-admin-service:latest \
  --push \
  ./admin-service

# Add other services as needed
```

## 🏗️ Service Architecture

```
KQICK Microservices
├── 🔐 auth-service (Port 3001)
├── 📦 product-service (Port 3002)
├── 👥 merchant-service (Port 3003)
├── 🛡️ admin-service (Port 3004)
└── 🌐 api-gateway (Port 3000)
```

## 📦 Built Images

After running the build script, you'll have these images:

- `kqick00/kqick-auth-service:latest`
- `kqick00/kqick-product-service:latest`
- `kqick00/kqick-merchant-service:latest`
- `kqick00/kqick-admin-service:latest`
- `kqick00/kqick-api-gateway:latest`

## 🚀 Deployment

### Local Development with Docker Compose
```bash
# Start all services locally
docker-compose up -d

# View logs
docker-compose logs -f

# Stop services
docker-compose down
```

### Production Deployment
The built images can be deployed to:
- **Kubernetes** clusters
- **Docker Swarm**
- **Cloud platforms** (AWS ECS, Google Cloud Run, Azure Container Instances)

## 🔧 Configuration

### Environment Variables
Each service uses these environment variables:

```env
NODE_ENV=production
MONGODB_URI=********************************:port/database
RABBITMQ_URL=amqp://username:password@host:port
REDIS_URL=redis://username:password@host:port
JWT_SECRET=your-jwt-secret
```

### Docker Compose Override
Create `docker-compose.override.yml` for local customizations:

```yaml
version: '3.8'
services:
  product-service:
    environment:
      - DEBUG=true
    ports:
      - "3002:3002"
```

## 🐛 Troubleshooting

### Common Issues

1. **Builder not found**
   ```bash
   docker buildx ls
   docker buildx create --driver cloud kqick00/kqick --name kqick-cloud-builder
   ```

2. **Authentication failed**
   ```bash
   docker login
   # Enter your Docker Hub credentials
   ```

3. **Build fails**
   ```bash
   # Check builder status
   docker buildx inspect kqick-cloud-builder
   
   # Restart builder
   docker buildx stop kqick-cloud-builder
   docker buildx start kqick-cloud-builder
   ```

## 📊 Monitoring

### Health Checks
Each service includes health check endpoints:
- `GET /health` - Basic health check
- `GET /metrics` - Prometheus metrics (if enabled)

### Logging
Services use structured logging with these levels:
- `error` - Error conditions
- `warn` - Warning conditions  
- `info` - Informational messages
- `debug` - Debug messages

## 🔒 Security

### Best Practices
1. **Use specific image tags** instead of `latest` in production
2. **Scan images** for vulnerabilities
3. **Use secrets management** for sensitive data
4. **Enable TLS** for all external communications
5. **Implement proper RBAC** in Kubernetes

### Image Scanning
```bash
# Scan images for vulnerabilities
docker scout cves kqick00/kqick-product-service:latest
```

## 📈 Performance

### Multi-platform Builds
The build scripts create images for both:
- `linux/amd64` (Intel/AMD processors)
- `linux/arm64` (ARM processors, Apple Silicon)

### Build Cache
Docker Build Cloud automatically handles build caching for faster subsequent builds.

## 🤝 Contributing

When adding new services:
1. Create a `Dockerfile` in the service directory
2. Add the service to `docker-compose.yml`
3. Update the build scripts
4. Test locally before pushing

## 📞 Support

For issues with:
- **Docker Build Cloud**: Contact Docker Support
- **KQICK Services**: Create an issue in the repository
- **Infrastructure**: Contact your DevOps team

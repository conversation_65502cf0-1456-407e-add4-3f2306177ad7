import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kqick_merchant/core/di/injection.dart';
import 'package:kqick_merchant/core/theme/app_theme.dart';
import 'package:kqick_merchant/features/products/presentation/bloc/product_bloc.dart';
import 'package:kqick_merchant/features/home/<USER>/pages/home_page.dart';

void main() {
  WidgetsFlutterBinding.ensureInitialized();
  setupDependencies();
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [BlocProvider(create: (_) => getIt<ProductBloc>())],
      child: MaterialApp(
        title: 'KQICK Merchant',
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,
        home: const HomePage(),
      ),
    );
  }
}

export declare class ProductVariantDto {
    id: string;
    name: string;
    price: number;
    stock: number;
    isAvailable: boolean;
}
export declare class CreateUniversalProductDto {
    name: string;
    description: string;
    category: string;
    subcategory: string;
    brand: string;
    suggestedPrice: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariantDto;
    };
    isActive?: boolean;
    tags?: string[];
    specifications?: {
        [key: string]: any;
    };
}
export declare class UpdateUniversalProductDto {
    name?: string;
    description?: string;
    category?: string;
    subcategory?: string;
    brand?: string;
    suggestedPrice?: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariantDto;
    };
    isActive?: boolean;
    tags?: string[];
    specifications?: {
        [key: string]: any;
    };
}
export declare class UniversalProductSearchDto {
    search?: string;
    category?: string;
    subcategory?: string;
    brand?: string;
    tags?: string[];
    minPrice?: number;
    maxPrice?: number;
    isActive?: boolean;
    page?: number;
    limit?: number;
    sortBy?: string;
    sortOrder?: 'asc' | 'desc';
}

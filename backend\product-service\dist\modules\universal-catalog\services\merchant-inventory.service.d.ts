import { Model } from 'mongoose';
import { MerchantInventory } from '../schemas/merchant-inventory.schema';
import { UniversalProduct } from '../schemas/universal-product.schema';
import { CreateMerchantInventoryDto, UpdateMerchantInventoryDto } from '../dto/universal-product.dto';
import { PaginatedResponse } from '../../../types/shared';
export declare class MerchantInventoryService {
    private readonly merchantInventoryModel;
    private readonly universalProductModel;
    constructor(merchantInventoryModel: Model<MerchantInventory>, universalProductModel: Model<UniversalProduct>);
    addProductToInventory(merchantId: string, createDto: CreateMerchantInventoryDto): Promise<MerchantInventory>;
    getMerchantInventory(merchantId: string, page?: number, limit?: number, search?: string, category?: string): Promise<PaginatedResponse<any>>;
    updateMerchantInventory(merchantId: string, inventoryId: string, updateDto: UpdateMerchantInventoryDto): Promise<MerchantInventory>;
    removeMerchantInventory(merchantId: string, inventoryId: string): Promise<void>;
    getMerchantInventoryByProduct(merchantId: string, universalProductId: string): Promise<MerchantInventory | null>;
    updateStock(merchantId: string, universalProductId: string, stockChange: number): Promise<MerchantInventory>;
    getLowStockItems(merchantId: string): Promise<MerchantInventory[]>;
}

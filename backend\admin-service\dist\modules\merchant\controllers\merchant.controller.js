"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantController = void 0;
const common_1 = require("@nestjs/common");
const merchant_service_1 = require("../services/merchant.service");
const jwt_auth_guard_1 = require("../../admin/guards/jwt-auth.guard");
const permissions_guard_1 = require("../../admin/guards/permissions.guard");
const permissions_decorator_1 = require("../../admin/decorators/permissions.decorator");
const admin_schema_1 = require("../../admin/schemas/admin.schema");
let MerchantController = class MerchantController {
    constructor(merchantService) {
        this.merchantService = merchantService;
    }
    async findAll(page, limit, search, status) {
        return this.merchantService.findAll({ page, limit, search, status });
    }
    async getMerchantAnalytics() {
        return this.merchantService.getMerchantAnalytics();
    }
    async findById(id) {
        return this.merchantService.findById(id);
    }
    async getMerchantInventory(id, page, limit) {
        return this.merchantService.getMerchantInventory(id, { page, limit });
    }
    async approveMerchant(id) {
        return this.merchantService.updateMerchantStatus(id, 'approved');
    }
    async rejectMerchant(id) {
        return this.merchantService.updateMerchantStatus(id, 'rejected');
    }
    async suspendMerchant(id) {
        return this.merchantService.updateMerchantStatus(id, 'suspended');
    }
    async activateMerchant(id) {
        return this.merchantService.updateMerchantStatus(id, 'active');
    }
    async deleteMerchant(id) {
        return this.merchantService.deleteMerchant(id);
    }
};
exports.MerchantController = MerchantController;
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('page')),
    __param(1, (0, common_1.Query)('limit')),
    __param(2, (0, common_1.Query)('search')),
    __param(3, (0, common_1.Query)('status')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Number, Number, String, String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('analytics'),
    (0, permissions_decorator_1.RequirePermissions)(admin_schema_1.AdminPermission.VIEW_ANALYTICS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "getMerchantAnalytics", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "findById", null);
__decorate([
    (0, common_1.Get)(':id/inventory'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Query)('page')),
    __param(2, (0, common_1.Query)('limit')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, Number, Number]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "getMerchantInventory", null);
__decorate([
    (0, common_1.Put)(':id/approve'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "approveMerchant", null);
__decorate([
    (0, common_1.Put)(':id/reject'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "rejectMerchant", null);
__decorate([
    (0, common_1.Put)(':id/suspend'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "suspendMerchant", null);
__decorate([
    (0, common_1.Put)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "activateMerchant", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], MerchantController.prototype, "deleteMerchant", null);
exports.MerchantController = MerchantController = __decorate([
    (0, common_1.Controller)('merchants'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, permissions_guard_1.PermissionsGuard),
    (0, permissions_decorator_1.RequirePermissions)(admin_schema_1.AdminPermission.MANAGE_MERCHANTS),
    __metadata("design:paramtypes", [merchant_service_1.MerchantService])
], MerchantController);
//# sourceMappingURL=merchant.controller.js.map
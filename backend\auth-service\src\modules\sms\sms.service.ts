import { Injectable, BadRequestException, HttpException, HttpStatus } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as twilio from 'twilio';
import { SmsRateLimiter } from './sms-rate-limiter.service';
import { SmsTemplates, formatPhoneNumber, validatePhoneNumber } from './sms-templates.service';
import { VerificationStore } from './verification-store.service';

@Injectable()
export class SmsService {
  private readonly client: twilio.Twilio;

  constructor(
    private readonly configService: ConfigService,
    private readonly rateLimiter: SmsRateLimiter,
    private readonly smsTemplates: SmsTemplates,
    private readonly verificationStore: VerificationStore,
  ) {
    const accountSid = this.configService.get<string>('twilio.accountSid');
    const authToken = this.configService.get<string>('twilio.authToken');
    this.client = twilio(accountSid, authToken);
  }

  async sendVerificationCode(phoneNumber: string): Promise<void> {
    const formattedPhone = formatPhoneNumber(phoneNumber);
    if (!validatePhoneNumber(formattedPhone)) {
      throw new BadRequestException('Invalid phone number format');
    }

    const canSend = await this.rateLimiter.checkRateLimit(formattedPhone);
    if (!canSend) {
      throw new TooManyRequestsException('Too many verification attempts. Please try again later.');
    }

    const code = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit code
    const message = this.smsTemplates.getVerificationTemplate(code);
    
    try {
      await this.sendSms(formattedPhone, message);
      await this.verificationStore.saveCode(formattedPhone, code);
    } catch (error) {
      throw new Error('Failed to send verification code');
    }
  }

  async verifyCode(phoneNumber: string, submittedCode: string): Promise<boolean> {
    const formattedPhone = formatPhoneNumber(phoneNumber);
    const savedCode = await this.verificationStore.getCode(formattedPhone);
    
    if (!savedCode) {
      return false;
    }
    
    const isValid = submittedCode === savedCode;
    if (isValid) {
      await this.verificationStore.removeCode(formattedPhone);
    } else {
      await this.rateLimiter.incrementFailedAttempts(formattedPhone);
    }
    
    return isValid;
  }

  async sendLoginAlert(phoneNumber: string, location: string): Promise<void> {
    const formattedPhone = formatPhoneNumber(phoneNumber);
    const time = new Date().toLocaleTimeString();
    const message = this.smsTemplates.getLoginAlertTemplate(location, time);
    await this.sendSms(formattedPhone, message);
  }

  async sendPasswordResetCode(phoneNumber: string): Promise<void> {
    const formattedPhone = formatPhoneNumber(phoneNumber);
    if (!validatePhoneNumber(formattedPhone)) {
      throw new BadRequestException('Invalid phone number format');
    }

    const code = Math.floor(100000 + Math.random() * 900000).toString();
    const message = this.smsTemplates.getPasswordResetTemplate(code);
    
    try {
      await this.sendSms(formattedPhone, message);
      await this.verificationStore.saveCode(formattedPhone, code, 10); // 10 minutes expiry
    } catch (error) {
      throw new Error('Failed to send password reset code');
    }
  }

  private async sendSms(to: string, message: string): Promise<boolean> {
    try {
      const fromNumber = this.configService.get<string>('twilio.fromNumber');
      await this.client.messages.create({
        body: message,
        from: fromNumber,
        to,
      });
      return true;
    } catch (error) {
      console.error('Twilio SMS error:', error);
      return false;
    }
  }
}

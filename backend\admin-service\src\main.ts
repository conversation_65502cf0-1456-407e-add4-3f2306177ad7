import { NestFactory } from '@nestjs/core';
import { ValidationPipe } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Transport, MicroserviceOptions } from '@nestjs/microservices';
import { AppModule } from './app.module';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);
  const configService = app.get(ConfigService);

  // Set up microservice transport
  app.connectMicroservice<MicroserviceOptions>({
    transport: Transport.RMQ,
    options: {
      urls: [configService.get<string>('rabbitmq.url')],
      queue: configService.get<string>('rabbitmq.queue'),
      queueOptions: {
        durable: true,
      },
    },
  });

  // Global validation pipe
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      transform: true,
      forbidNonWhitelisted: true,
    }),
  );

  // Enable CORS for admin dashboard
  app.enableCors({
    origin: [
      'http://localhost:3001', // Admin dashboard
      'http://localhost:3000', // API Gateway
    ],
    credentials: true,
  });

  // Start microservice and HTTP server
  await app.startAllMicroservices();
  await app.listen(configService.get<number>('port') || 3004);
  
  console.log(`Admin service is running on: ${await app.getUrl()}`);
}

bootstrap();

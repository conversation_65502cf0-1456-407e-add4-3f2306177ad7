import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { Admin } from '../schemas/admin.schema';
import { CreateAdminDto, UpdateAdminDto, LoginDto, ChangePasswordDto, AdminResponseDto } from '../dto/admin.dto';
export declare class AdminService {
    private readonly adminModel;
    private readonly jwtService;
    constructor(adminModel: Model<Admin>, jwtService: JwtService);
    createAdmin(createAdminDto: CreateAdminDto): Promise<AdminResponseDto>;
    login(loginDto: LoginDto): Promise<{
        admin: AdminResponseDto;
        accessToken: string;
        refreshToken: string;
    }>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    logout(adminId: string): Promise<void>;
    findAll(): Promise<AdminResponseDto[]>;
    findById(id: string): Promise<AdminResponseDto>;
    updateAdmin(id: string, updateAdminDto: UpdateAdminDto): Promise<AdminResponseDto>;
    changePassword(adminId: string, changePasswordDto: ChangePasswordDto): Promise<void>;
    deleteAdmin(id: string): Promise<void>;
    private getDefaultPermissions;
    private toResponseDto;
}

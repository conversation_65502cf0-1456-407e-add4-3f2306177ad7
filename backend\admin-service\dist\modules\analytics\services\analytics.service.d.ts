import { ClientProxy } from '@nestjs/microservices';
export declare class AnalyticsService {
    private readonly productClient;
    private readonly merchantClient;
    constructor(productClient: ClientProxy, merchantClient: ClientProxy);
    getDashboardAnalytics(): Promise<{
        products: {
            total: any;
            active: any;
            categories: any;
        };
        merchants: {
            total: any;
            active: any;
            pending: any;
        };
        summary: {
            totalProducts: any;
            totalMerchants: any;
            activeMerchants: any;
            pendingMerchants: any;
        };
    }>;
    getProductAnalytics(startDate?: string, endDate?: string): Promise<any>;
    getMerchantAnalytics(startDate?: string, endDate?: string): Promise<any>;
    getSalesAnalytics(startDate?: string, endDate?: string, groupBy?: 'day' | 'week' | 'month'): Promise<any>;
    getTopProducts(limit?: number, period?: string): Promise<any>;
    getTopMerchants(limit?: number, period?: string): Promise<any>;
    private getProductStats;
    private getMerchantStats;
}

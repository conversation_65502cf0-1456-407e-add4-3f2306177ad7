import { Module } from '@nestjs/common';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MongooseModule } from '@nestjs/mongoose';
import { ClientsModule, Transport } from '@nestjs/microservices';
import configuration from './config/configuration';
import { AdminModule } from './modules/admin/admin.module';
import { UniversalProductModule } from './modules/universal-product/universal-product.module';
import { MerchantModule } from './modules/merchant/merchant.module';
import { AnalyticsModule } from './modules/analytics/analytics.module';
import { SERVICES } from '@kqick/shared-lib';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
    }),
    MongooseModule.forRootAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => ({
        uri: configService.get<string>('mongodb.uri'),
      }),
      inject: [ConfigService],
    }),
    ClientsModule.registerAsync([
      {
        name: SERVICES.PRODUCT,
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('rabbitmq.url')],
            queue: 'product_queue',
            queueOptions: {
              durable: true,
            },
          },
        }),
        inject: [ConfigService],
      },
      {
        name: SERVICES.MERCHANT,
        imports: [ConfigModule],
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.RMQ,
          options: {
            urls: [configService.get<string>('rabbitmq.url')],
            queue: 'merchant_queue',
            queueOptions: {
              durable: true,
            },
          },
        }),
        inject: [ConfigService],
      },
    ]),
    AdminModule,
    UniversalProductModule,
    MerchantModule,
    AnalyticsModule,
  ],
})
export class AppModule {}

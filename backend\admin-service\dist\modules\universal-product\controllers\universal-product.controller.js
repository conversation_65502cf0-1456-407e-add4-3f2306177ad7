"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalProductController = void 0;
const common_1 = require("@nestjs/common");
const universal_product_service_1 = require("../services/universal-product.service");
const universal_product_dto_1 = require("../dto/universal-product.dto");
const jwt_auth_guard_1 = require("../../admin/guards/jwt-auth.guard");
const permissions_guard_1 = require("../../admin/guards/permissions.guard");
const permissions_decorator_1 = require("../../admin/decorators/permissions.decorator");
const admin_schema_1 = require("../../admin/schemas/admin.schema");
let UniversalProductController = class UniversalProductController {
    constructor(universalProductService) {
        this.universalProductService = universalProductService;
    }
    async createProduct(createDto) {
        return this.universalProductService.createProduct(createDto);
    }
    async findAll(searchDto) {
        return this.universalProductService.findAll(searchDto);
    }
    async getCategories() {
        return this.universalProductService.getCategories();
    }
    async getSubcategories(category) {
        return this.universalProductService.getSubcategories(category);
    }
    async getBrands(category, subcategory) {
        return this.universalProductService.getBrands(category, subcategory);
    }
    async searchByBarcode(barcode) {
        return this.universalProductService.searchByBarcode(barcode);
    }
    async searchBySku(sku) {
        return this.universalProductService.searchBySku(sku);
    }
    async getProductAnalytics() {
        return this.universalProductService.getProductAnalytics();
    }
    async findById(id) {
        return this.universalProductService.findById(id);
    }
    async updateProduct(id, updateDto) {
        return this.universalProductService.updateProduct(id, updateDto);
    }
    async deleteProduct(id) {
        return this.universalProductService.deleteProduct(id);
    }
    async bulkImport(products) {
        return this.universalProductService.bulkImport(products);
    }
    async activateProduct(id) {
        return this.universalProductService.updateProduct(id, { isActive: true });
    }
    async deactivateProduct(id) {
        return this.universalProductService.updateProduct(id, { isActive: false });
    }
};
exports.UniversalProductController = UniversalProductController;
__decorate([
    (0, common_1.Post)(),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [universal_product_dto_1.CreateUniversalProductDto]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "createProduct", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [universal_product_dto_1.UniversalProductSearchDto]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)('categories'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "getCategories", null);
__decorate([
    (0, common_1.Get)('subcategories'),
    __param(0, (0, common_1.Query)('category')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "getSubcategories", null);
__decorate([
    (0, common_1.Get)('brands'),
    __param(0, (0, common_1.Query)('category')),
    __param(1, (0, common_1.Query)('subcategory')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "getBrands", null);
__decorate([
    (0, common_1.Get)('search/barcode/:barcode'),
    __param(0, (0, common_1.Param)('barcode')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "searchByBarcode", null);
__decorate([
    (0, common_1.Get)('search/sku/:sku'),
    __param(0, (0, common_1.Param)('sku')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "searchBySku", null);
__decorate([
    (0, common_1.Get)('analytics'),
    (0, permissions_decorator_1.RequirePermissions)(admin_schema_1.AdminPermission.VIEW_ANALYTICS),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "getProductAnalytics", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "findById", null);
__decorate([
    (0, common_1.Put)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, universal_product_dto_1.UpdateUniversalProductDto]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "updateProduct", null);
__decorate([
    (0, common_1.Delete)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "deleteProduct", null);
__decorate([
    (0, common_1.Post)('bulk-import'),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Array]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "bulkImport", null);
__decorate([
    (0, common_1.Put)(':id/activate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "activateProduct", null);
__decorate([
    (0, common_1.Put)(':id/deactivate'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", Promise)
], UniversalProductController.prototype, "deactivateProduct", null);
exports.UniversalProductController = UniversalProductController = __decorate([
    (0, common_1.Controller)('universal-products'),
    (0, common_1.UseGuards)(jwt_auth_guard_1.JwtAuthGuard, permissions_guard_1.PermissionsGuard),
    (0, permissions_decorator_1.RequirePermissions)(admin_schema_1.AdminPermission.MANAGE_PRODUCTS),
    __metadata("design:paramtypes", [universal_product_service_1.UniversalProductService])
], UniversalProductController);
//# sourceMappingURL=universal-product.controller.js.map
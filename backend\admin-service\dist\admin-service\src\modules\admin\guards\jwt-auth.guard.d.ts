import { JwtService } from '@nestjs/jwt';
import { Model } from 'mongoose';
import { Admin } from '../schemas/admin.schema';
declare const JwtAuthGuard_base: import("@nestjs/passport").Type<import("@nestjs/passport").IAuthGuard>;
export declare class JwtAuthGuard extends JwtAuthGuard_base {
    private readonly jwtService;
    private readonly adminModel;
    constructor(jwtService: JwtService, adminModel: Model<Admin>);
    canActivate(context: any): Promise<boolean>;
    private extractTokenFromHeader;
}
export {};

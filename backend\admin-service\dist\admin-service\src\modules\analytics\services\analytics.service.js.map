{"version": 3, "file": "analytics.service.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/analytics/services/analytics.service.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA,2CAAoD;AACpD,yDAAoD;AACpD,+BAAsC;AACtC,8DAA6C;AAGtC,IAAM,gBAAgB,GAAtB,MAAM,gBAAgB;IAC3B,YAC6C,aAA0B,EACzB,cAA2B;QAD5B,kBAAa,GAAb,aAAa,CAAa;QACzB,mBAAc,GAAd,cAAc,CAAa;IACtE,CAAC;IAEJ,KAAK,CAAC,qBAAqB;QACzB,IAAI,CAAC;YACH,MAAM,CAAC,YAAY,EAAE,aAAa,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACtD,IAAI,CAAC,eAAe,EAAE;gBACtB,IAAI,CAAC,gBAAgB,EAAE;aACxB,CAAC,CAAC;YAEH,OAAO;gBACL,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,aAAa;gBACxB,OAAO,EAAE;oBACP,aAAa,EAAE,YAAY,CAAC,KAAK;oBACjC,cAAc,EAAE,aAAa,CAAC,KAAK;oBACnC,eAAe,EAAE,aAAa,CAAC,MAAM;oBACrC,gBAAgB,EAAE,aAAa,CAAC,OAAO;iBACxC;aACF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,QAAQ,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE;gBAChD,SAAS,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE;gBAC9C,OAAO,EAAE;oBACP,aAAa,EAAE,CAAC;oBAChB,cAAc,EAAE,CAAC;oBACjB,eAAe,EAAE,CAAC;oBAClB,gBAAgB,EAAE,CAAC;iBACpB;aACF,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,mBAAmB,CAAC,SAAkB,EAAE,OAAgB;QAC5D,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,qBAAc,EACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,uBAAuB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CACzE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,aAAa,EAAE,CAAC;gBAChB,WAAW,EAAE,CAAC;gBACd,mBAAmB,EAAE,EAAE;gBACvB,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,oBAAoB,CAAC,SAAkB,EAAE,OAAgB;QAC7D,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,qBAAc,EACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,wBAAwB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC,CAC3E,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,cAAc,EAAE,CAAC;gBACjB,YAAY,EAAE,CAAC;gBACf,eAAe,EAAE,CAAC;gBAClB,eAAe,EAAE,EAAE;aACpB,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,iBAAiB,CAAC,SAAkB,EAAE,OAAgB,EAAE,UAAoC,KAAK;QACrG,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,qBAAc,EACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,qBAAqB,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,OAAO,EAAE,CAAC,CACjF,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO;gBACL,UAAU,EAAE,CAAC;gBACb,SAAS,EAAE,EAAE;gBACb,MAAM,EAAE,CAAC;aACV,CAAC;QACJ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,cAAc,CAAC,QAAgB,EAAE,EAAE,MAAe;QACtD,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,qBAAc,EACzB,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kBAAkB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAC/D,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAED,KAAK,CAAC,eAAe,CAAC,QAAgB,EAAE,EAAE,MAAe;QACvD,IAAI,CAAC;YACH,OAAO,MAAM,IAAA,qBAAc,EACzB,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CACjE,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,CAAC;QACZ,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,eAAe;QAC3B,IAAI,CAAC;YACH,MAAM,CAAC,aAAa,EAAE,UAAU,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;gBACpD,IAAA,qBAAc,EACZ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,EAAE,CAAC,CAC9E;gBACD,IAAA,qBAAc,EACZ,IAAI,CAAC,aAAa,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,CAAC,CAChE;aACF,CAAC,CAAC;YAEH,OAAO;gBACL,KAAK,EAAE,aAAa,CAAC,KAAK,IAAI,CAAC;gBAC/B,MAAM,EAAE,aAAa,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,MAAM,IAAI,CAAC;gBACtE,UAAU,EAAE,UAAU,CAAC,MAAM,IAAI,CAAC;aACnC,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,UAAU,EAAE,CAAC,EAAE,CAAC;QAChD,CAAC;IACH,CAAC;IAEO,KAAK,CAAC,gBAAgB;QAC5B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,MAAM,IAAA,qBAAc,EACpC,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,oBAAoB,EAAE,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC,CACzE,CAAC;YAEF,MAAM,KAAK,GAAG,SAAS,CAAC,KAAK,IAAI,CAAC,CAAC;YACnC,MAAM,MAAM,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,QAAQ,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;YACrF,MAAM,OAAO,GAAG,SAAS,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC,CAAM,EAAE,EAAE,CAAC,CAAC,CAAC,MAAM,KAAK,SAAS,CAAC,CAAC,MAAM,IAAI,CAAC,CAAC;YAEvF,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;QACpC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,EAAE,KAAK,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,OAAO,EAAE,CAAC,EAAE,CAAC;QAC7C,CAAC;IACH,CAAC;CACF,CAAA;AAzIY,4CAAgB;2BAAhB,gBAAgB;IAD5B,IAAA,mBAAU,GAAE;IAGR,WAAA,IAAA,eAAM,EAAC,qBAAQ,CAAC,OAAO,CAAC,CAAA;IACxB,WAAA,IAAA,eAAM,EAAC,qBAAQ,CAAC,QAAQ,CAAC,CAAA;qCADgC,2BAAW;QACT,2BAAW;GAH9D,gBAAgB,CAyI5B"}
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = () => ({
    port: parseInt(process.env.PORT, 10) || 3004,
    mongodb: {
        uri: process.env.MONGODB_URI || 'mongodb+srv://lesibamosese03:<EMAIL>/kqick_admin?retryWrites=true&w=majority&appName=Cluster0',
    },
    rabbitmq: {
        url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
        queue: 'admin_queue',
    },
    redis: {
        url: process.env.REDIS_URL || 'redis://localhost:6379',
    },
    jwt: {
        secret: process.env.JWT_SECRET || 'admin-secret-key',
        expiresIn: process.env.JWT_EXPIRES_IN || '24h',
    },
});
//# sourceMappingURL=configuration.js.map
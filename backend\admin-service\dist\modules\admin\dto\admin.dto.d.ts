import { AdminRole, AdminPermission } from '../schemas/admin.schema';
export declare class CreateAdminDto {
    email: string;
    password: string;
    firstName: string;
    lastName: string;
    role: AdminRole;
    permissions?: AdminPermission[];
    phoneNumber?: string;
}
export declare class UpdateAdminDto {
    firstName?: string;
    lastName?: string;
    role?: AdminRole;
    permissions?: AdminPermission[];
    isActive?: boolean;
    phoneNumber?: string;
    profileImage?: string;
}
export declare class LoginDto {
    email: string;
    password: string;
}
export declare class ChangePasswordDto {
    currentPassword: string;
    newPassword: string;
}
export declare class AdminResponseDto {
    id: string;
    email: string;
    firstName: string;
    lastName: string;
    role: AdminRole;
    permissions: AdminPermission[];
    isActive: boolean;
    lastLogin?: Date;
    profileImage?: string;
    phoneNumber?: string;
    createdAt: Date;
    updatedAt: Date;
}

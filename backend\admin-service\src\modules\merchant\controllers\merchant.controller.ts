import { Controller, Get, Put, Delete, Param, Body, Query, UseGuards } from '@nestjs/common';
import { MerchantService } from '../services/merchant.service';
import { JwtAuthGuard } from '../../admin/guards/jwt-auth.guard';
import { PermissionsGuard } from '../../admin/guards/permissions.guard';
import { RequirePermissions } from '../../admin/decorators/permissions.decorator';
import { AdminPermission } from '../../admin/schemas/admin.schema';

@Controller('merchants')
@UseGuards(JwtAuthGuard, PermissionsGuard)
@RequirePermissions(AdminPermission.MANAGE_MERCHANTS)
export class MerchantController {
  constructor(private readonly merchantService: MerchantService) {}

  @Get()
  async findAll(
    @Query('page') page?: number,
    @Query('limit') limit?: number,
    @Query('search') search?: string,
    @Query('status') status?: string,
  ) {
    return this.merchantService.findAll({ page, limit, search, status });
  }

  @Get('analytics')
  @RequirePermissions(AdminPermission.VIEW_ANALYTICS)
  async getMerchantAnalytics() {
    return this.merchantService.getMerchantAnalytics();
  }

  @Get(':id')
  async findById(@Param('id') id: string) {
    return this.merchantService.findById(id);
  }

  @Get(':id/inventory')
  async getMerchantInventory(
    @Param('id') id: string,
    @Query('page') page?: number,
    @Query('limit') limit?: number,
  ) {
    return this.merchantService.getMerchantInventory(id, { page, limit });
  }

  @Put(':id/approve')
  async approveMerchant(@Param('id') id: string) {
    return this.merchantService.updateMerchantStatus(id, 'approved');
  }

  @Put(':id/reject')
  async rejectMerchant(@Param('id') id: string) {
    return this.merchantService.updateMerchantStatus(id, 'rejected');
  }

  @Put(':id/suspend')
  async suspendMerchant(@Param('id') id: string) {
    return this.merchantService.updateMerchantStatus(id, 'suspended');
  }

  @Put(':id/activate')
  async activateMerchant(@Param('id') id: string) {
    return this.merchantService.updateMerchantStatus(id, 'active');
  }

  @Delete(':id')
  async deleteMerchant(@Param('id') id: string) {
    return this.merchantService.deleteMerchant(id);
  }
}

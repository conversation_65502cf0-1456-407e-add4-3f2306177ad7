import { Model } from 'mongoose';
import { UniversalProduct } from '../schemas/universal-product.schema';
import { MerchantInventory } from '../schemas/merchant-inventory.schema';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
import { PaginatedResponse } from '@kqick/shared-lib';
export declare class UniversalCatalogService {
    private readonly universalProductModel;
    private readonly merchantInventoryModel;
    constructor(universalProductModel: Model<UniversalProduct>, merchantInventoryModel: Model<MerchantInventory>);
    createUniversalProduct(createDto: CreateUniversalProductDto): Promise<UniversalProduct>;
    findAllUniversalProducts(searchDto: UniversalProductSearchDto): Promise<PaginatedResponse<UniversalProduct>>;
    findUniversalProductById(id: string): Promise<UniversalProduct>;
    updateUniversalProduct(id: string, updateDto: UpdateUniversalProductDto): Promise<UniversalProduct>;
    deleteUniversalProduct(id: string): Promise<void>;
    getCategories(): Promise<string[]>;
    getSubcategories(category?: string): Promise<string[]>;
    getBrands(category?: string, subcategory?: string): Promise<string[]>;
    searchByBarcode(barcode: string): Promise<UniversalProduct | null>;
    searchBySku(sku: string): Promise<UniversalProduct | null>;
}

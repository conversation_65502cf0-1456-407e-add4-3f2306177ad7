import { ProductVariant } from '@kqick/shared-lib';
export declare class CreateProductDto {
    name: string;
    description: string;
    category: string;
    basePrice: number;
    imageUrl?: string;
    barcode?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isAvailable?: boolean;
}
export declare class UpdateProductDto {
    name?: string;
    description?: string;
    category?: string;
    basePrice?: number;
    imageUrl?: string;
    barcode?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isAvailable?: boolean;
}
export declare class UpdateInventoryDto {
    quantity: number;
    price: number;
    isAvailable?: boolean;
}

"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantInventorySchema = exports.MerchantInventory = void 0;
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
let MerchantInventory = class MerchantInventory extends mongoose_2.Document {
};
exports.MerchantInventory = MerchantInventory;
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: String }),
    __metadata("design:type", String)
], MerchantInventory.prototype, "merchantId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, type: mongoose_2.Schema.Types.ObjectId, ref: 'UniversalProduct' }),
    __metadata("design:type", mongoose_2.Schema.Types.ObjectId)
], MerchantInventory.prototype, "universalProductId", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true }),
    __metadata("design:type", Number)
], MerchantInventory.prototype, "price", void 0);
__decorate([
    (0, mongoose_1.Prop)({ required: true, default: 0 }),
    __metadata("design:type", Number)
], MerchantInventory.prototype, "stock", void 0);
__decorate([
    (0, mongoose_1.Prop)({ default: true }),
    __metadata("design:type", Boolean)
], MerchantInventory.prototype, "isAvailable", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], MerchantInventory.prototype, "minStock", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", Number)
], MerchantInventory.prototype, "maxStock", void 0);
__decorate([
    (0, mongoose_1.Prop)(),
    __metadata("design:type", String)
], MerchantInventory.prototype, "location", void 0);
exports.MerchantInventory = MerchantInventory = __decorate([
    (0, mongoose_1.Schema)({ timestamps: true })
], MerchantInventory);
exports.MerchantInventorySchema = mongoose_1.SchemaFactory.createForClass(MerchantInventory);
exports.MerchantInventorySchema.index({ merchantId: 1, universalProductId: 1 }, { unique: true });
exports.MerchantInventorySchema.index({ merchantId: 1 });
exports.MerchantInventorySchema.index({ universalProductId: 1 });
exports.MerchantInventorySchema.index({ isAvailable: 1 });
exports.MerchantInventorySchema.index({ stock: 1 });
//# sourceMappingURL=merchant-inventory.schema.js.map
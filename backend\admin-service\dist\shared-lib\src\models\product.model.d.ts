export declare class ProductVariant {
    id: string;
    name: string;
    price: number;
    stock: number;
    isAvailable: boolean;
}
export declare class Product {
    id: string;
    name: string;
    description: string;
    category: string;
    basePrice: number;
    imageUrl?: string;
    barcode?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isAvailable: boolean;
    createdAt: Date;
    updatedAt: Date;
}
export declare class UniversalProduct {
    id: string;
    name: string;
    description: string;
    category: string;
    subcategory: string;
    brand: string;
    suggestedPrice: number;
    imageUrl?: string;
    barcode?: string;
    sku?: string;
    variants?: {
        [key: string]: ProductVariant;
    };
    isActive: boolean;
    tags?: string[];
    specifications?: {
        [key: string]: any;
    };
    createdAt: Date;
    updatedAt: Date;
}
export declare class MerchantProductInventory {
    id: string;
    merchantId: string;
    universalProductId: string;
    price: number;
    stock: number;
    isAvailable: boolean;
    minStock?: number;
    maxStock?: number;
    location?: string;
    createdAt: Date;
    updatedAt: Date;
}

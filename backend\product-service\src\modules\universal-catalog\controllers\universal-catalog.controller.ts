import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { UniversalCatalogService } from '../services/universal-catalog.service';
import {
  CreateUniversalProductDto,
  UpdateUniversalProductDto,
  UniversalProductSearchDto,
} from '../dto/universal-product.dto';
import { TOPICS } from '@kqick/shared-lib';

@Controller()
export class UniversalCatalogController {
  constructor(
    private readonly universalCatalogService: UniversalCatalogService,
  ) {}

  @MessagePattern(TOPICS.UNIVERSAL_PRODUCT_CREATED)
  createUniversalProduct(@Payload() createDto: CreateUniversalProductDto) {
    return this.universalCatalogService.createUniversalProduct(createDto);
  }

  @MessagePattern('find_all_universal_products')
  findAllUniversalProducts(@Payload() searchDto: UniversalProductSearchDto) {
    return this.universalCatalogService.findAllUniversalProducts(searchDto);
  }

  @MessagePattern('find_universal_product_by_id')
  findUniversalProductById(@Payload() data: { id: string }) {
    return this.universalCatalogService.findUniversalProductById(data.id);
  }

  @MessagePattern(TOPICS.UNIVERSAL_PRODUCT_UPDATED)
  updateUniversalProduct(
    @Payload() data: { id: string; updateDto: UpdateUniversalProductDto },
  ) {
    return this.universalCatalogService.updateUniversalProduct(
      data.id,
      data.updateDto,
    );
  }

  @MessagePattern(TOPICS.UNIVERSAL_PRODUCT_DELETED)
  deleteUniversalProduct(@Payload() data: { id: string }) {
    return this.universalCatalogService.deleteUniversalProduct(data.id);
  }

  @MessagePattern('get_universal_product_categories')
  getCategories() {
    return this.universalCatalogService.getCategories();
  }

  @MessagePattern('get_universal_product_subcategories')
  getSubcategories(@Payload() data: { category?: string }) {
    return this.universalCatalogService.getSubcategories(data.category);
  }

  @MessagePattern('get_universal_product_brands')
  getBrands(
    @Payload() data: { category?: string; subcategory?: string },
  ) {
    return this.universalCatalogService.getBrands(
      data.category,
      data.subcategory,
    );
  }

  @MessagePattern('search_universal_product_by_barcode')
  searchByBarcode(@Payload() data: { barcode: string }) {
    return this.universalCatalogService.searchByBarcode(data.barcode);
  }

  @MessagePattern('search_universal_product_by_sku')
  searchBySku(@Payload() data: { sku: string }) {
    return this.universalCatalogService.searchBySku(data.sku);
  }
}

import 'package:get_it/get_it.dart';
import 'package:kqick_merchant/core/network/api_client.dart';
import 'package:kqick_merchant/features/products/data/product_repository.dart';
import 'package:kqick_merchant/features/products/presentation/bloc/product_bloc.dart';
import 'package:kqick_merchant/features/universal_catalog/data/repositories/universal_catalog_repository.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_bloc.dart';

final getIt = GetIt.instance;

void setupDependencies() {
  // Core
  getIt.registerLazySingleton(
    () => ApiClient(
      baseUrl: 'https://api.kqick.co.za/v1', // Replace with your actual API URL
    ),
  );

  // Repositories
  getIt.registerLazySingleton(
    () => ProductRepository(apiClient: getIt<ApiClient>()),
  );

  getIt.registerLazySingleton<UniversalCatalogRepository>(
    () => UniversalCatalogRepositoryImpl(getIt<ApiClient>()),
  );

  // BLoCs
  getIt.registerFactory(
    () => ProductBloc(productRepository: getIt<ProductRepository>()),
  );

  getIt.registerFactory(
    () => UniversalCatalogBloc(repository: getIt<UniversalCatalogRepository>()),
  );
}

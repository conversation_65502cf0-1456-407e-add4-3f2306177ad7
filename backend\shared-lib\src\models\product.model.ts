import { IsString, IsN<PERSON>ber, IsOptional, IsBoolean, IsDate, IsObject, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';

export class ProductVariant {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsNumber()
  price: number;

  @IsNumber()
  stock: number;

  @IsBoolean()
  isAvailable: boolean;
}

export class Product {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  category: string;

  @IsNumber()
  basePrice: number;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  barcode?: string;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductVariant)
  variants?: { [key: string]: ProductVariant };

  @IsBoolean()
  isAvailable: boolean;

  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}

export class UniversalProduct {
  @IsString()
  id: string;

  @IsString()
  name: string;

  @IsString()
  description: string;

  @IsString()
  category: string;

  @IsString()
  subcategory: string;

  @IsString()
  brand: string;

  @IsNumber()
  suggestedPrice: number;

  @IsOptional()
  @IsString()
  imageUrl?: string;

  @IsOptional()
  @IsString()
  barcode?: string;

  @IsOptional()
  @IsString()
  sku?: string;

  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => ProductVariant)
  variants?: { [key: string]: ProductVariant };

  @IsBoolean()
  isActive: boolean;

  @IsOptional()
  @IsString()
  tags?: string[];

  @IsOptional()
  @IsObject()
  specifications?: { [key: string]: any };

  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}

export class MerchantProductInventory {
  @IsString()
  id: string;

  @IsString()
  merchantId: string;

  @IsString()
  universalProductId: string;

  @IsNumber()
  price: number;

  @IsNumber()
  stock: number;

  @IsBoolean()
  isAvailable: boolean;

  @IsOptional()
  @IsNumber()
  minStock?: number;

  @IsOptional()
  @IsNumber()
  maxStock?: number;

  @IsOptional()
  @IsString()
  location?: string;

  @IsDate()
  @Type(() => Date)
  createdAt: Date;

  @IsDate()
  @Type(() => Date)
  updatedAt: Date;
}

{"version": 3, "file": "change_stream_cursor.js", "sourceRoot": "", "sources": ["../../src/cursor/change_stream_cursor.ts"], "names": [], "mappings": ";;;AACA,oDAM0B;AAE1B,4CAA8C;AAE9C,uDAA6D;AAE7D,uEAAmE;AAEnE,oCAAiE;AACjE,uDAI2B;AAY3B,gBAAgB;AAChB,MAAa,kBAGX,SAAQ,gCAA2C;IAenD,YACE,MAAmB,EACnB,SAA2B,EAC3B,WAAuB,EAAE,EACzB,UAAqC,EAAE;QAEvC,KAAK,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,GAAG,OAAO,EAAE,QAAQ,EAAE,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE,CAAC,CAAC;QAE1E,IAAI,CAAC,QAAQ,GAAG,QAAQ,CAAC;QACzB,IAAI,CAAC,yBAAyB,GAAG,OAAO,CAAC;QACzC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC;QACzB,IAAI,CAAC,oBAAoB,GAAG,OAAO,CAAC,oBAAoB,IAAI,IAAI,CAAC;QAEjE,IAAI,OAAO,CAAC,UAAU,EAAE,CAAC;YACvB,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,UAAU,CAAC;QACxC,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC/B,IAAI,CAAC,WAAW,GAAG,OAAO,CAAC,WAAW,CAAC;QACzC,CAAC;IACH,CAAC;IAED,IAAI,WAAW,CAAC,KAAkB;QAChC,IAAI,CAAC,YAAY,GAAG,KAAK,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,4BAAY,CAAC,oBAAoB,EAAE,KAAK,CAAC,CAAC;IACtD,CAAC;IAED,IAAI,WAAW;QACb,OAAO,IAAI,CAAC,YAAY,CAAC;IAC3B,CAAC;IAED,IAAI,aAAa;QACf,MAAM,OAAO,GAA8B;YACzC,GAAG,IAAI,CAAC,yBAAyB;SAClC,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,YAAY,EAAE,sBAAsB,CAAU,EAAE,CAAC;YACjF,OAAO,OAAO,CAAC,GAAG,CAAC,CAAC;QACtB,CAAC;QAED,IAAI,IAAI,CAAC,WAAW,IAAI,IAAI,EAAE,CAAC;YAC7B,IAAI,IAAI,CAAC,yBAAyB,CAAC,UAAU,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;gBACnE,OAAO,CAAC,UAAU,GAAG,IAAI,CAAC,WAAW,CAAC;YACxC,CAAC;iBAAM,CAAC;gBACN,OAAO,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACzC,CAAC;QACH,CAAC;aAAM,IAAI,IAAI,CAAC,oBAAoB,IAAI,IAAI,IAAI,IAAA,sBAAc,EAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC;YACjF,OAAO,CAAC,oBAAoB,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC3D,CAAC;QAED,OAAO,OAAO,CAAC;IACjB,CAAC;IAED,gBAAgB,CAAC,WAAwB;QACvC,IAAI,IAAI,CAAC,aAAa,EAAE,KAAK,CAAC,IAAI,IAAI,CAAC,oBAAoB,EAAE,CAAC;YAC5D,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,oBAAoB,CAAC;QAC/C,CAAC;aAAM,CAAC;YACN,IAAI,CAAC,WAAW,GAAG,WAAW,CAAC;QACjC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC;IAC1B,CAAC;IAED,aAAa,CAAC,QAAwB;QACpC,MAAM,EAAE,oBAAoB,EAAE,GAAG,QAAQ,CAAC;QAC1C,IAAI,oBAAoB,EAAE,CAAC;YACzB,IAAI,CAAC,oBAAoB,GAAG,oBAAoB,CAAC;YAEjD,IAAI,QAAQ,CAAC,SAAS,KAAK,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,WAAW,GAAG,oBAAoB,CAAC;YAC1C,CAAC;QACH,CAAC;IACH,CAAC;IAED,KAAK;QACH,OAAO,IAAI,kBAAkB,CAAC,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE;YACxE,GAAG,IAAI,CAAC,aAAa;SACtB,CAAC,CAAC;IACL,CAAC;IAED,KAAK,CAAC,WAAW,CAAC,OAAsB;QACtC,MAAM,kBAAkB,GAAG,IAAI,8BAAkB,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,QAAQ,EAAE;YAC/E,GAAG,IAAI,CAAC,aAAa;YACrB,GAAG,IAAI,CAAC,yBAAyB;YACjC,OAAO;SACR,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAG,MAAM,IAAA,oCAAgB,EACrC,OAAO,CAAC,MAAM,EACd,kBAAkB,EAClB,IAAI,CAAC,cAAc,CACpB,CAAC;QAEF,MAAM,MAAM,GAAG,kBAAkB,CAAC,MAAM,CAAC;QACzC,IAAI,CAAC,cAAc,GAAG,IAAA,sBAAc,EAAC,MAAM,CAAC,CAAC;QAE7C,IACE,IAAI,CAAC,oBAAoB,IAAI,IAAI;YACjC,IAAI,CAAC,yBAAyB,CAAC,WAAW,IAAI,IAAI;YAClD,IAAI,CAAC,yBAAyB,CAAC,UAAU,IAAI,IAAI;YACjD,IAAI,CAAC,cAAc,IAAI,CAAC,EACxB,CAAC;YACD,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,aAAa,CAAC;QACrD,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,gBAAI,EAAE,QAAQ,CAAC,CAAC;QAC1B,IAAI,CAAC,IAAI,CAAC,oBAAQ,CAAC,CAAC;QAEpB,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC;IACvC,CAAC;IAEQ,KAAK,CAAC,OAAO,CAAC,SAAiB;QACtC,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC;QAEhD,IAAI,CAAC,cAAc,GAAG,IAAA,sBAAc,EAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAClD,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;QAE7B,IAAI,CAAC,IAAI,CAAC,4BAAY,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;QACvC,IAAI,CAAC,IAAI,CAAC,4BAAY,CAAC,QAAQ,CAAC,CAAC;QACjC,OAAO,QAAQ,CAAC;IAClB,CAAC;CACF;AA1ID,gDA0IC"}
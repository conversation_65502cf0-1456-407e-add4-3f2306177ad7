import { <PERSON>p, Schem<PERSON>, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ProductVariant } from '@kqick/shared-lib';

@Schema({ timestamps: true })
export class UniversalProduct extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  subcategory: string;

  @Prop({ required: true })
  brand: string;

  @Prop({ required: true })
  suggestedPrice: number;

  @Prop()
  imageUrl?: string;

  @Prop({ unique: true, sparse: true })
  barcode?: string;

  @Prop({ unique: true, sparse: true })
  sku?: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  variants?: { [key: string]: ProductVariant };

  @Prop({ default: true })
  isActive: boolean;

  @Prop({ type: [String], default: [] })
  tags: string[];

  @Prop({ type: MongooseSchema.Types.Mixed })
  specifications?: { [key: string]: any };
}

export const UniversalProductSchema = SchemaFactory.createForClass(UniversalProduct);

// Create indexes for better search performance
UniversalProductSchema.index({ name: 'text', description: 'text', brand: 'text', tags: 'text' });
UniversalProductSchema.index({ category: 1, subcategory: 1 });
UniversalProductSchema.index({ brand: 1 });
UniversalProductSchema.index({ barcode: 1 });
UniversalProductSchema.index({ sku: 1 });
UniversalProductSchema.index({ isActive: 1 });

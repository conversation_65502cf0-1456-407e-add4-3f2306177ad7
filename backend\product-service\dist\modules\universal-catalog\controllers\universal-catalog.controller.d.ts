import { UniversalCatalogService } from '../services/universal-catalog.service';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
export declare class UniversalCatalogController {
    private readonly universalCatalogService;
    constructor(universalCatalogService: UniversalCatalogService);
    createUniversalProduct(createDto: CreateUniversalProductDto): Promise<import("../schemas/universal-product.schema").UniversalProduct>;
    findAllUniversalProducts(searchDto: UniversalProductSearchDto): Promise<import("../../../types/shared").PaginatedResponse<import("../schemas/universal-product.schema").UniversalProduct>>;
    findUniversalProductById(data: {
        id: string;
    }): Promise<import("../schemas/universal-product.schema").UniversalProduct>;
    updateUniversalProduct(data: {
        id: string;
        updateDto: UpdateUniversalProductDto;
    }): Promise<import("../schemas/universal-product.schema").UniversalProduct>;
    deleteUniversalProduct(data: {
        id: string;
    }): Promise<void>;
    getCategories(): Promise<string[]>;
    getSubcategories(data: {
        category?: string;
    }): Promise<string[]>;
    getBrands(data: {
        category?: string;
        subcategory?: string;
    }): Promise<string[]>;
    searchByBarcode(data: {
        barcode: string;
    }): Promise<import("../schemas/universal-product.schema").UniversalProduct | null>;
    searchBySku(data: {
        sku: string;
    }): Promise<import("../schemas/universal-product.schema").UniversalProduct | null>;
}

version: '3.8'

services:
  mongodb:
    image: mongo:latest
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: kqick
      MONGO_INITDB_ROOT_PASSWORD: kqickdev
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass kqickdev
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3-management
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: kqick
      RABBITMQ_DEFAULT_PASS: kqickdev
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # Service containers will be added here as we develop them
  
volumes:
  mongodb_data:
  redis_data:
  rabbitmq_data:

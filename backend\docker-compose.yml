version: '3.8'

services:
  mongodb:
    image: mongo:latest
    restart: always
    ports:
      - "27017:27017"
    environment:
      MONGO_INITDB_ROOT_USERNAME: kqick
      MONGO_INITDB_ROOT_PASSWORD: kqickdev
    volumes:
      - mongodb_data:/data/db

  redis:
    image: redis:alpine
    restart: always
    ports:
      - "6379:6379"
    command: redis-server --requirepass kqickdev
    volumes:
      - redis_data:/data

  rabbitmq:
    image: rabbitmq:3-management
    restart: always
    ports:
      - "5672:5672"
      - "15672:15672"
    environment:
      RABBITMQ_DEFAULT_USER: kqick
      RABBITMQ_DEFAULT_PASS: kqickdev
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq

  # API Gateway
  api-gateway:
    build:
      context: ./api-gateway
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672
      - REDIS_URL=redis://:kqickdev@redis:6379
    depends_on:
      - rabbitmq
      - redis
    volumes:
      - ./api-gateway:/app
      - /app/node_modules

  # Auth Service
  auth-service:
    build:
      context: ./auth-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*************************************************
      - RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672
      - REDIS_URL=redis://:kqickdev@redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis
    volumes:
      - ./auth-service:/app
      - /app/node_modules

  # Product Service
  product-service:
    build:
      context: ./product-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - MONGODB_URI=*****************************************************
      - RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672
      - REDIS_URL=redis://:kqickdev@redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis
    volumes:
      - ./product-service:/app
      - /app/node_modules

  # Merchant Service
  merchant-service:
    build:
      context: ./merchant-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - MONGODB_URI=******************************************************
      - RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672
      - REDIS_URL=redis://:kqickdev@redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis
    volumes:
      - ./merchant-service:/app
      - /app/node_modules

  # Admin Service
  admin-service:
    build:
      context: ./admin-service
      dockerfile: Dockerfile
    environment:
      - NODE_ENV=development
      - MONGODB_URI=**************************************************
      - RABBITMQ_URL=amqp://kqick:kqickdev@rabbitmq:5672
      - REDIS_URL=redis://:kqickdev@redis:6379
    depends_on:
      - mongodb
      - rabbitmq
      - redis
    volumes:
      - ./admin-service:/app
      - /app/node_modules

volumes:
  mongodb_data:
  redis_data:
  rabbitmq_data:

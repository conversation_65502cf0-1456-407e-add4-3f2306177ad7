"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const product_schema_1 = require("./schemas/product.schema");
const shared_1 = require("../../types/shared");
let ProductService = class ProductService {
    constructor(productModel) {
        this.productModel = productModel;
    }
    async create(merchantId, createProductDto) {
        const product = new this.productModel(Object.assign(Object.assign({}, createProductDto), { merchantId }));
        return product.save();
    }
    async findAll(merchantId, query) {
        const { page = 1, limit = 20, search, category, sort, order = 'asc' } = query;
        const skip = (page - 1) * limit;
        const filter = { merchantId };
        if (search) {
            filter.$or = [
                { name: new RegExp(search, 'i') },
                { description: new RegExp(search, 'i') },
                { barcode: new RegExp(search, 'i') },
            ];
        }
        if (category) {
            filter.category = category;
        }
        const sortOptions = {};
        if (sort) {
            sortOptions[sort] = order === 'asc' ? 1 : -1;
        }
        else {
            sortOptions.updatedAt = -1;
        }
        const [products, total] = await Promise.all([
            this.productModel
                .find(filter)
                .sort(sortOptions)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.productModel.countDocuments(filter),
        ]);
        return {
            data: products,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            hasMore: total > skip + products.length,
        };
    }
    async findOne(merchantId, id) {
        const product = await this.productModel.findOne({ _id: id, merchantId }).exec();
        if (!product) {
            throw new shared_1.NotFoundError('Product not found');
        }
        return product;
    }
    async update(merchantId, id, updateProductDto) {
        const product = await this.productModel
            .findOneAndUpdate({ _id: id, merchantId }, Object.assign(Object.assign({}, updateProductDto), { updatedAt: new Date() }), { new: true })
            .exec();
        if (!product) {
            throw new shared_1.NotFoundError('Product not found');
        }
        return product;
    }
    async updateInventory(merchantId, id, updateInventoryDto) {
        var _a;
        const product = await this.productModel
            .findOneAndUpdate({ _id: id, merchantId }, {
            basePrice: updateInventoryDto.price,
            isAvailable: (_a = updateInventoryDto.isAvailable) !== null && _a !== void 0 ? _a : (updateInventoryDto.quantity > 0),
            updatedAt: new Date(),
        }, { new: true })
            .exec();
        if (!product) {
            throw new shared_1.NotFoundError('Product not found');
        }
        return product;
    }
    async remove(merchantId, id) {
        const result = await this.productModel.deleteOne({ _id: id, merchantId }).exec();
        if (result.deletedCount === 0) {
            throw new shared_1.NotFoundError('Product not found');
        }
    }
    async getCategories(merchantId) {
        const categories = await this.productModel.distinct('category', { merchantId }).exec();
        return categories.sort();
    }
};
exports.ProductService = ProductService;
exports.ProductService = ProductService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(product_schema_1.Product.name)),
    __metadata("design:paramtypes", [mongoose_2.Model])
], ProductService);
//# sourceMappingURL=product.service.js.map
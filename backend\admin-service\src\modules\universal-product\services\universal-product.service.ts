import { Injectable, Inject } from '@nestjs/common';
import { ClientProxy } from '@nestjs/microservices';
import { firstValueFrom } from 'rxjs';
import { CreateUniversalProductDto, UpdateUniversalProductDto, UniversalProductSearchDto } from '../dto/universal-product.dto';
import { SERVICES, TOPICS } from '../../../constants/shared';

@Injectable()
export class UniversalProductService {
  constructor(
    @Inject(SERVICES.PRODUCT) private readonly productClient: ClientProxy,
  ) {}

  async createProduct(createDto: CreateUniversalProductDto) {
    return firstValueFrom(
      this.productClient.send(TOPICS.UNIVERSAL_PRODUCT_CREATED, createDto),
    );
  }

  async findAll(searchDto: UniversalProductSearchDto) {
    return firstValueFrom(
      this.productClient.send('find_all_universal_products', searchDto),
    );
  }

  async findById(id: string) {
    return firstValueFrom(
      this.productClient.send('find_universal_product_by_id', { id }),
    );
  }

  async updateProduct(id: string, updateDto: UpdateUniversalProductDto) {
    return firstValueFrom(
      this.productClient.send(TOPICS.UNIVERSAL_PRODUCT_UPDATED, { id, updateDto }),
    );
  }

  async deleteProduct(id: string) {
    return firstValueFrom(
      this.productClient.send(TOPICS.UNIVERSAL_PRODUCT_DELETED, { id }),
    );
  }

  async getCategories() {
    return firstValueFrom(
      this.productClient.send('get_universal_product_categories', {}),
    );
  }

  async getSubcategories(category?: string) {
    return firstValueFrom(
      this.productClient.send('get_universal_product_subcategories', { category }),
    );
  }

  async getBrands(category?: string, subcategory?: string) {
    return firstValueFrom(
      this.productClient.send('get_universal_product_brands', { category, subcategory }),
    );
  }

  async searchByBarcode(barcode: string) {
    return firstValueFrom(
      this.productClient.send('search_universal_product_by_barcode', { barcode }),
    );
  }

  async searchBySku(sku: string) {
    return firstValueFrom(
      this.productClient.send('search_universal_product_by_sku', { sku }),
    );
  }

  async bulkImport(products: CreateUniversalProductDto[]) {
    const results = [];
    for (const product of products) {
      try {
        const result = await this.createProduct(product);
        results.push({ success: true, product: result });
      } catch (error) {
        results.push({ success: false, error: error.message, product });
      }
    }
    return {
      total: products.length,
      successful: results.filter(r => r.success).length,
      failed: results.filter(r => !r.success).length,
      results,
    };
  }

  async getProductAnalytics() {
    // This would typically aggregate data from multiple sources
    const [categories, totalProducts] = await Promise.all([
      this.getCategories(),
      this.findAll({ page: 1, limit: 1 }),
    ]);

    return {
      totalProducts: totalProducts.total || 0,
      totalCategories: categories.length,
      // Add more analytics as needed
    };
  }
}

export interface PaginationQuery {
    page?: number;
    limit?: number;
    sort?: string;
    order?: 'asc' | 'desc';
}
export interface SearchQuery extends PaginationQuery {
    search?: string;
    category?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    hasMore: boolean;
}
export interface ApiResponse<T> {
    success: boolean;
    message?: string;
    data?: T;
    error?: any;
}

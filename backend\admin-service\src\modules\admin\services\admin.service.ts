import { Injectable, UnauthorizedException, ConflictException, NotFoundException } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import * as bcrypt from 'bcrypt';
import { Admin, AdminRole, AdminPermission } from '../schemas/admin.schema';
import { CreateAdminDto, UpdateAdminDto, LoginDto, ChangePasswordDto, AdminResponseDto } from '../dto/admin.dto';

@Injectable()
export class AdminService {
  constructor(
    @InjectModel(Admin.name) private readonly adminModel: Model<Admin>,
    private readonly jwtService: JwtService,
  ) {}

  async createAdmin(createAdminDto: CreateAdminDto): Promise<AdminResponseDto> {
    const existingAdmin = await this.adminModel.findOne({ email: createAdminDto.email });
    if (existingAdmin) {
      throw new ConflictException('Admin with this email already exists');
    }

    const hashedPassword = await bcrypt.hash(createAdminDto.password, 12);
    
    const admin = new this.adminModel({
      ...createAdminDto,
      password: hashedPassword,
      permissions: createAdminDto.permissions || this.getDefaultPermissions(createAdminDto.role),
    });

    const savedAdmin = await admin.save();
    return this.toResponseDto(savedAdmin);
  }

  async login(loginDto: LoginDto): Promise<{ admin: AdminResponseDto; accessToken: string; refreshToken: string }> {
    const admin = await this.adminModel.findOne({ email: loginDto.email });
    if (!admin || !admin.isActive) {
      throw new UnauthorizedException('Invalid credentials');
    }

    const isPasswordValid = await bcrypt.compare(loginDto.password, admin.password);
    if (!isPasswordValid) {
      throw new UnauthorizedException('Invalid credentials');
    }

    // Update last login
    admin.lastLogin = new Date();
    await admin.save();

    const payload = { sub: admin._id, email: admin.email, role: admin.role };
    const accessToken = this.jwtService.sign(payload);
    const refreshToken = this.jwtService.sign(payload, { expiresIn: '7d' });

    // Store refresh token
    admin.refreshToken = refreshToken;
    await admin.save();

    return {
      admin: this.toResponseDto(admin),
      accessToken,
      refreshToken,
    };
  }

  async refreshToken(refreshToken: string): Promise<{ accessToken: string; refreshToken: string }> {
    try {
      const payload = this.jwtService.verify(refreshToken);
      const admin = await this.adminModel.findById(payload.sub);
      
      if (!admin || admin.refreshToken !== refreshToken || !admin.isActive) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      const newPayload = { sub: admin._id, email: admin.email, role: admin.role };
      const newAccessToken = this.jwtService.sign(newPayload);
      const newRefreshToken = this.jwtService.sign(newPayload, { expiresIn: '7d' });

      admin.refreshToken = newRefreshToken;
      await admin.save();

      return {
        accessToken: newAccessToken,
        refreshToken: newRefreshToken,
      };
    } catch (error) {
      throw new UnauthorizedException('Invalid refresh token');
    }
  }

  async logout(adminId: string): Promise<void> {
    await this.adminModel.findByIdAndUpdate(adminId, { refreshToken: null });
  }

  async findAll(): Promise<AdminResponseDto[]> {
    const admins = await this.adminModel.find().sort({ createdAt: -1 });
    return admins.map(admin => this.toResponseDto(admin));
  }

  async findById(id: string): Promise<AdminResponseDto> {
    const admin = await this.adminModel.findById(id);
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    return this.toResponseDto(admin);
  }

  async updateAdmin(id: string, updateAdminDto: UpdateAdminDto): Promise<AdminResponseDto> {
    const admin = await this.adminModel.findByIdAndUpdate(id, updateAdminDto, { new: true });
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }
    return this.toResponseDto(admin);
  }

  async changePassword(adminId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    const admin = await this.adminModel.findById(adminId);
    if (!admin) {
      throw new NotFoundException('Admin not found');
    }

    const isCurrentPasswordValid = await bcrypt.compare(changePasswordDto.currentPassword, admin.password);
    if (!isCurrentPasswordValid) {
      throw new UnauthorizedException('Current password is incorrect');
    }

    const hashedNewPassword = await bcrypt.hash(changePasswordDto.newPassword, 12);
    admin.password = hashedNewPassword;
    await admin.save();
  }

  async deleteAdmin(id: string): Promise<void> {
    const result = await this.adminModel.findByIdAndDelete(id);
    if (!result) {
      throw new NotFoundException('Admin not found');
    }
  }

  private getDefaultPermissions(role: AdminRole): AdminPermission[] {
    switch (role) {
      case AdminRole.SUPER_ADMIN:
        return Object.values(AdminPermission);
      case AdminRole.ADMIN:
        return [
          AdminPermission.MANAGE_PRODUCTS,
          AdminPermission.MANAGE_MERCHANTS,
          AdminPermission.MANAGE_ORDERS,
          AdminPermission.VIEW_ANALYTICS,
        ];
      case AdminRole.MODERATOR:
        return [
          AdminPermission.MANAGE_PRODUCTS,
          AdminPermission.VIEW_ANALYTICS,
        ];
      default:
        return [];
    }
  }

  private toResponseDto(admin: Admin): AdminResponseDto {
    return {
      id: admin._id.toString(),
      email: admin.email,
      firstName: admin.firstName,
      lastName: admin.lastName,
      role: admin.role,
      permissions: admin.permissions,
      isActive: admin.isActive,
      lastLogin: admin.lastLogin,
      profileImage: admin.profileImage,
      phoneNumber: admin.phoneNumber,
      createdAt: admin.createdAt,
      updatedAt: admin.updatedAt,
    };
  }
}

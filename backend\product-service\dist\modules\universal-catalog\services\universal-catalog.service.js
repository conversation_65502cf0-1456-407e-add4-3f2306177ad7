"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalCatalogService = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const mongoose_2 = require("mongoose");
const universal_product_schema_1 = require("../schemas/universal-product.schema");
const merchant_inventory_schema_1 = require("../schemas/merchant-inventory.schema");
const shared_lib_1 = require("@kqick/shared-lib");
let UniversalCatalogService = class UniversalCatalogService {
    constructor(universalProductModel, merchantInventoryModel) {
        this.universalProductModel = universalProductModel;
        this.merchantInventoryModel = merchantInventoryModel;
    }
    async createUniversalProduct(createDto) {
        const product = new this.universalProductModel(createDto);
        return product.save();
    }
    async findAllUniversalProducts(searchDto) {
        const { search, category, subcategory, brand, tags, minPrice, maxPrice, page = 1, limit = 20, sortBy = 'createdAt', sortOrder = 'desc', } = searchDto;
        const query = { isActive: true };
        if (search) {
            query.$text = { $search: search };
        }
        if (category) {
            query.category = category;
        }
        if (subcategory) {
            query.subcategory = subcategory;
        }
        if (brand) {
            query.brand = new RegExp(brand, 'i');
        }
        if (tags && tags.length > 0) {
            query.tags = { $in: tags };
        }
        if (minPrice !== undefined || maxPrice !== undefined) {
            query.suggestedPrice = {};
            if (minPrice !== undefined) {
                query.suggestedPrice.$gte = minPrice;
            }
            if (maxPrice !== undefined) {
                query.suggestedPrice.$lte = maxPrice;
            }
        }
        const skip = (page - 1) * limit;
        const sortOptions = {};
        sortOptions[sortBy] = sortOrder === 'asc' ? 1 : -1;
        const [products, total] = await Promise.all([
            this.universalProductModel
                .find(query)
                .sort(sortOptions)
                .skip(skip)
                .limit(limit)
                .exec(),
            this.universalProductModel.countDocuments(query),
        ]);
        return {
            data: products,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
        };
    }
    async findUniversalProductById(id) {
        const product = await this.universalProductModel.findById(id);
        if (!product) {
            throw new shared_lib_1.NotFoundError('Universal product not found');
        }
        return product;
    }
    async updateUniversalProduct(id, updateDto) {
        const product = await this.universalProductModel.findByIdAndUpdate(id, updateDto, { new: true });
        if (!product) {
            throw new shared_lib_1.NotFoundError('Universal product not found');
        }
        return product;
    }
    async deleteUniversalProduct(id) {
        const result = await this.universalProductModel.findByIdAndDelete(id);
        if (!result) {
            throw new shared_lib_1.NotFoundError('Universal product not found');
        }
    }
    async getCategories() {
        const categories = await this.universalProductModel.distinct('category', {
            isActive: true,
        });
        return categories.sort();
    }
    async getSubcategories(category) {
        const query = { isActive: true };
        if (category) {
            query.category = category;
        }
        const subcategories = await this.universalProductModel.distinct('subcategory', query);
        return subcategories.sort();
    }
    async getBrands(category, subcategory) {
        const query = { isActive: true };
        if (category) {
            query.category = category;
        }
        if (subcategory) {
            query.subcategory = subcategory;
        }
        const brands = await this.universalProductModel.distinct('brand', query);
        return brands.sort();
    }
    async searchByBarcode(barcode) {
        return this.universalProductModel.findOne({ barcode, isActive: true });
    }
    async searchBySku(sku) {
        return this.universalProductModel.findOne({ sku, isActive: true });
    }
};
exports.UniversalCatalogService = UniversalCatalogService;
exports.UniversalCatalogService = UniversalCatalogService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, mongoose_1.InjectModel)(universal_product_schema_1.UniversalProduct.name)),
    __param(1, (0, mongoose_1.InjectModel)(merchant_inventory_schema_1.MerchantInventory.name)),
    __metadata("design:paramtypes", [mongoose_2.Model,
        mongoose_2.Model])
], UniversalCatalogService);
//# sourceMappingURL=universal-catalog.service.js.map
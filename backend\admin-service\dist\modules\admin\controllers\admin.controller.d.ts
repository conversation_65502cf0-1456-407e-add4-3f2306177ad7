import { AdminService } from '../services/admin.service';
import { CreateAdminDto, UpdateAdminDto, LoginDto, ChangePasswordDto } from '../dto/admin.dto';
export declare class AdminController {
    private readonly adminService;
    constructor(adminService: AdminService);
    login(loginDto: LoginDto): Promise<{
        admin: import("../dto/admin.dto").AdminResponseDto;
        accessToken: string;
        refreshToken: string;
    }>;
    refreshToken(refreshToken: string): Promise<{
        accessToken: string;
        refreshToken: string;
    }>;
    logout(req: any): Promise<void>;
    createAdmin(createAdminDto: CreateAdminDto): Promise<import("../dto/admin.dto").AdminResponseDto>;
    findAll(): Promise<import("../dto/admin.dto").AdminResponseDto[]>;
    getProfile(req: any): Promise<import("../dto/admin.dto").AdminResponseDto>;
    findById(id: string): Promise<import("../dto/admin.dto").AdminResponseDto>;
    updateProfile(req: any, updateAdminDto: UpdateAdminDto): Promise<import("../dto/admin.dto").AdminResponseDto>;
    updateAdmin(id: string, updateAdminDto: UpdateAdminDto): Promise<import("../dto/admin.dto").AdminResponseDto>;
    changePassword(req: any, changePasswordDto: ChangePasswordDto): Promise<void>;
    deleteAdmin(id: string): Promise<void>;
}

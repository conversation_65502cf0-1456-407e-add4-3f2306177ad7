import { Modu<PERSON> } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { UniversalCatalogService } from './services/universal-catalog.service';
import { MerchantInventoryService } from './services/merchant-inventory.service';
import { UniversalCatalogController } from './controllers/universal-catalog.controller';
import { MerchantInventoryController } from './controllers/merchant-inventory.controller';
import {
  UniversalProduct,
  UniversalProductSchema,
} from './schemas/universal-product.schema';
import {
  MerchantInventory,
  MerchantInventorySchema,
} from './schemas/merchant-inventory.schema';

@Module({
  imports: [
    MongooseModule.forFeature([
      { name: UniversalProduct.name, schema: UniversalProductSchema },
      { name: MerchantInventory.name, schema: MerchantInventorySchema },
    ]),
  ],
  controllers: [UniversalCatalogController, MerchantInventoryController],
  providers: [UniversalCatalogService, MerchantInventoryService],
  exports: [UniversalCatalogService, MerchantInventoryService],
})
export class UniversalCatalogModule {}

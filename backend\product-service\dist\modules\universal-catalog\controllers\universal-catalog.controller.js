"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalCatalogController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const universal_catalog_service_1 = require("../services/universal-catalog.service");
const universal_product_dto_1 = require("../dto/universal-product.dto");
const shared_lib_1 = require("@kqick/shared-lib");
let UniversalCatalogController = class UniversalCatalogController {
    constructor(universalCatalogService) {
        this.universalCatalogService = universalCatalogService;
    }
    createUniversalProduct(createDto) {
        return this.universalCatalogService.createUniversalProduct(createDto);
    }
    findAllUniversalProducts(searchDto) {
        return this.universalCatalogService.findAllUniversalProducts(searchDto);
    }
    findUniversalProductById(data) {
        return this.universalCatalogService.findUniversalProductById(data.id);
    }
    updateUniversalProduct(data) {
        return this.universalCatalogService.updateUniversalProduct(data.id, data.updateDto);
    }
    deleteUniversalProduct(data) {
        return this.universalCatalogService.deleteUniversalProduct(data.id);
    }
    getCategories() {
        return this.universalCatalogService.getCategories();
    }
    getSubcategories(data) {
        return this.universalCatalogService.getSubcategories(data.category);
    }
    getBrands(data) {
        return this.universalCatalogService.getBrands(data.category, data.subcategory);
    }
    searchByBarcode(data) {
        return this.universalCatalogService.searchByBarcode(data.barcode);
    }
    searchBySku(data) {
        return this.universalCatalogService.searchBySku(data.sku);
    }
};
exports.UniversalCatalogController = UniversalCatalogController;
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.UNIVERSAL_PRODUCT_CREATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [universal_product_dto_1.CreateUniversalProductDto]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "createUniversalProduct", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_all_universal_products'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [universal_product_dto_1.UniversalProductSearchDto]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "findAllUniversalProducts", null);
__decorate([
    (0, microservices_1.MessagePattern)('find_universal_product_by_id'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "findUniversalProductById", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.UNIVERSAL_PRODUCT_UPDATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "updateUniversalProduct", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.UNIVERSAL_PRODUCT_DELETED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "deleteUniversalProduct", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_universal_product_categories'),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", []),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "getCategories", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_universal_product_subcategories'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "getSubcategories", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_universal_product_brands'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "getBrands", null);
__decorate([
    (0, microservices_1.MessagePattern)('search_universal_product_by_barcode'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "searchByBarcode", null);
__decorate([
    (0, microservices_1.MessagePattern)('search_universal_product_by_sku'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], UniversalCatalogController.prototype, "searchBySku", null);
exports.UniversalCatalogController = UniversalCatalogController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [universal_catalog_service_1.UniversalCatalogService])
], UniversalCatalogController);
//# sourceMappingURL=universal-catalog.controller.js.map
{"name": "auth-service", "version": "1.0.0", "main": "index.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@nestjs/common": "^11.1.1", "@nestjs/config": "^4.0.2", "@nestjs/core": "^11.1.1", "@nestjs/jwt": "^11.0.0", "@nestjs/microservices": "^11.1.1", "@nestjs/mongoose": "^11.0.3", "@nestjs/passport": "^11.0.5", "@nestjs/platform-express": "^11.1.1", "bcrypt": "^6.0.0", "class-transformer": "^0.5.1", "class-validator": "^0.14.2", "mongoose": "^8.15.0", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "reflect-metadata": "^0.2.2", "rxjs": "^7.8.2", "twilio": "^5.6.1"}, "devDependencies": {"@nestjs/testing": "^11.1.1", "@types/amqplib": "^0.10.7", "@types/bcrypt": "^5.0.2", "@types/jest": "^29.5.14", "@types/node": "^22.15.21", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "jest": "^29.7.0", "ts-jest": "^29.3.4", "typescript": "^5.8.3"}}
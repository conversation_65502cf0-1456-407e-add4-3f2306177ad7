import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kqick_merchant/core/theme/app_colors.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_bloc.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_event.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_state.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/widgets/universal_product_grid_item.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/widgets/universal_product_search_bar.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/widgets/universal_product_filters.dart';
import 'package:kqick_merchant/shared/models/universal_product.dart';

class UniversalCatalogPage extends StatefulWidget {
  const UniversalCatalogPage({super.key});

  @override
  State<UniversalCatalogPage> createState() => _UniversalCatalogPageState();
}

class _UniversalCatalogPageState extends State<UniversalCatalogPage> {
  final ScrollController _scrollController = ScrollController();
  String? _selectedCategory;
  String? _selectedSubcategory;
  String? _selectedBrand;
  String? _searchQuery;
  double? _minPrice;
  double? _maxPrice;
  String? _sortBy;
  String? _sortOrder;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadProducts();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<UniversalCatalogBloc>().add(const LoadMoreUniversalProducts());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  void _loadProducts({bool refresh = false}) {
    context.read<UniversalCatalogBloc>().add(
          LoadUniversalProducts(
            refresh: refresh,
            search: _searchQuery,
            category: _selectedCategory,
            subcategory: _selectedSubcategory,
            brand: _selectedBrand,
            minPrice: _minPrice,
            maxPrice: _maxPrice,
            sortBy: _sortBy,
            sortOrder: _sortOrder,
          ),
        );
  }

  void _onSearchChanged(String query) {
    setState(() => _searchQuery = query.isEmpty ? null : query);
    _loadProducts(refresh: true);
  }

  void _onFiltersChanged({
    String? category,
    String? subcategory,
    String? brand,
    double? minPrice,
    double? maxPrice,
  }) {
    setState(() {
      _selectedCategory = category;
      _selectedSubcategory = subcategory;
      _selectedBrand = brand;
      _minPrice = minPrice;
      _maxPrice = maxPrice;
    });
    _loadProducts(refresh: true);
  }

  void _onSortChanged(String? sortBy, String? sortOrder) {
    setState(() {
      _sortBy = sortBy;
      _sortOrder = sortOrder;
    });
    _loadProducts(refresh: true);
  }

  void _showProductDetails(UniversalProduct product) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _ProductDetailsBottomSheet(product: product),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Universal Product Catalog'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFiltersDialog(),
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.sort),
            onSelected: (value) {
              final parts = value.split('_');
              _onSortChanged(parts[0], parts[1]);
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'name_asc',
                child: Text('Name (A-Z)'),
              ),
              const PopupMenuItem(
                value: 'name_desc',
                child: Text('Name (Z-A)'),
              ),
              const PopupMenuItem(
                value: 'suggestedPrice_asc',
                child: Text('Price (Low to High)'),
              ),
              const PopupMenuItem(
                value: 'suggestedPrice_desc',
                child: Text('Price (High to Low)'),
              ),
              const PopupMenuItem(
                value: 'createdAt_desc',
                child: Text('Newest First'),
              ),
              const PopupMenuItem(
                value: 'createdAt_asc',
                child: Text('Oldest First'),
              ),
            ],
          ),
        ],
      ),
      body: Column(
        children: [
          UniversalProductSearchBar(
            onSearchChanged: _onSearchChanged,
            onBarcodeScanned: (barcode) {
              context.read<UniversalCatalogBloc>().add(
                    SearchUniversalProductByBarcode(barcode),
                  );
            },
          ),
          if (_hasActiveFilters()) _buildActiveFiltersChips(),
          Expanded(
            child: BlocConsumer<UniversalCatalogBloc, UniversalCatalogState>(
              listener: (context, state) {
                if (state is UniversalCatalogError) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(state.message),
                      backgroundColor: Colors.red,
                    ),
                  );
                } else if (state is UniversalProductFound) {
                  _showProductDetails(state.product);
                } else if (state is UniversalProductNotFound) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('No product found for: ${state.searchTerm}'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                } else if (state is ProductAddedToInventory) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Product added to inventory successfully!'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
              builder: (context, state) {
                if (state is UniversalCatalogLoading) {
                  return const Center(child: CircularProgressIndicator());
                } else if (state is UniversalProductsLoaded) {
                  if (state.products.isEmpty) {
                    return const Center(
                      child: Text('No products found'),
                    );
                  }

                  return RefreshIndicator(
                    onRefresh: () async => _loadProducts(refresh: true),
                    child: GridView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 2,
                        childAspectRatio: 0.75,
                        mainAxisSpacing: 16,
                        crossAxisSpacing: 16,
                      ),
                      itemCount: state.hasReachedMax
                          ? state.products.length
                          : state.products.length + 1,
                      itemBuilder: (context, index) {
                        if (index >= state.products.length) {
                          return const Center(child: CircularProgressIndicator());
                        }
                        final product = state.products[index];
                        return UniversalProductGridItem(
                          product: product,
                          onTap: () => _showProductDetails(product),
                        );
                      },
                    ),
                  );
                }

                return const Center(
                  child: Text('Start searching for products'),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  bool _hasActiveFilters() {
    return _selectedCategory != null ||
        _selectedSubcategory != null ||
        _selectedBrand != null ||
        _minPrice != null ||
        _maxPrice != null;
  }

  Widget _buildActiveFiltersChips() {
    return Container(
      height: 50,
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView(
        scrollDirection: Axis.horizontal,
        children: [
          if (_selectedCategory != null)
            _buildFilterChip('Category: $_selectedCategory', () {
              setState(() => _selectedCategory = null);
              _loadProducts(refresh: true);
            }),
          if (_selectedSubcategory != null)
            _buildFilterChip('Subcategory: $_selectedSubcategory', () {
              setState(() => _selectedSubcategory = null);
              _loadProducts(refresh: true);
            }),
          if (_selectedBrand != null)
            _buildFilterChip('Brand: $_selectedBrand', () {
              setState(() => _selectedBrand = null);
              _loadProducts(refresh: true);
            }),
          if (_minPrice != null || _maxPrice != null)
            _buildFilterChip(
              'Price: ${_minPrice ?? 0} - ${_maxPrice ?? '∞'}',
              () {
                setState(() {
                  _minPrice = null;
                  _maxPrice = null;
                });
                _loadProducts(refresh: true);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildFilterChip(String label, VoidCallback onDeleted) {
    return Padding(
      padding: const EdgeInsets.only(right: 8),
      child: Chip(
        label: Text(label),
        onDeleted: onDeleted,
        deleteIcon: const Icon(Icons.close, size: 18),
      ),
    );
  }

  void _showFiltersDialog() {
    showDialog(
      context: context,
      builder: (context) => UniversalProductFilters(
        selectedCategory: _selectedCategory,
        selectedSubcategory: _selectedSubcategory,
        selectedBrand: _selectedBrand,
        minPrice: _minPrice,
        maxPrice: _maxPrice,
        onFiltersChanged: _onFiltersChanged,
      ),
    );
  }
}

class _ProductDetailsBottomSheet extends StatelessWidget {
  final UniversalProduct product;

  const _ProductDetailsBottomSheet({required this.product});

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) {
        return Container(
          decoration: const BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              Container(
                width: 40,
                height: 4,
                margin: const EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (product.imageUrl != null)
                        Container(
                          height: 200,
                          width: double.infinity,
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            image: DecorationImage(
                              image: NetworkImage(product.imageUrl!),
                              fit: BoxFit.cover,
                            ),
                          ),
                        ),
                      const SizedBox(height: 16),
                      Text(
                        product.name,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        product.brand,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              color: AppColors.primary,
                            ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'R${product.suggestedPrice.toStringAsFixed(2)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              color: Colors.green,
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Description',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      Text(product.description),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          Expanded(
                            child: _buildInfoCard('Category', product.category),
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: _buildInfoCard('Subcategory', product.subcategory),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      if (product.barcode != null)
                        _buildInfoCard('Barcode', product.barcode!),
                      if (product.sku != null)
                        _buildInfoCard('SKU', product.sku!),
                      const SizedBox(height: 24),
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => _showAddToInventoryDialog(context),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primary,
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                          child: const Text('Add to My Inventory'),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildInfoCard(String label, String value) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.grey,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showAddToInventoryDialog(BuildContext context) {
    // This would show a dialog to add the product to inventory
    // with fields for price, stock, etc.
    Navigator.pop(context); // Close the bottom sheet first
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Add to Inventory'),
        content: const Text('Add to inventory dialog would go here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              // Add to inventory logic
              Navigator.pop(context);
            },
            child: const Text('Add'),
          ),
        ],
      ),
    );
  }
}

"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOPICS = exports.NotFoundError = exports.ProductVariant = void 0;
class ProductVariant {
}
exports.ProductVariant = ProductVariant;
class NotFoundError extends Error {
    constructor(message) {
        super(message);
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
exports.TOPICS = {
    PRODUCT_CREATED: 'product.created',
    PRODUCT_UPDATED: 'product.updated',
    PRODUCT_DELETED: 'product.deleted',
    INVENTORY_UPDATED: 'inventory.updated',
    UNIVERSAL_PRODUCT_CREATED: 'universal.product.created',
    UNIVERSAL_PRODUCT_UPDATED: 'universal.product.updated',
    UNIVERSAL_PRODUCT_DELETED: 'universal.product.deleted',
    MERCHANT_INVENTORY_CREATED: 'merchant.inventory.created',
    MERCHANT_INVENTORY_UPDATED: 'merchant.inventory.updated',
    MERCHANT_INVENTORY_DELETED: 'merchant.inventory.deleted',
};
//# sourceMappingURL=shared.js.map
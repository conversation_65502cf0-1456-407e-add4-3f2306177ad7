import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ProductService } from './product.service';
import { CreateProductDto, UpdateProductDto, UpdateInventoryDto } from './dto/product.dto';
import { SearchQuery, TOPICS } from '../../types/shared';

@Controller()
export class ProductController {
  constructor(private readonly productService: ProductService) {}

  @MessagePattern(TOPICS.PRODUCT_CREATED)
  create(@Payload() data: { merchantId: string; product: CreateProductDto }) {
    return this.productService.create(data.merchantId, data.product);
  }

  @MessagePattern('find_all_products')
  findAll(@Payload() data: { merchantId: string; query: SearchQuery }) {
    return this.productService.findAll(data.merchantId, data.query);
  }

  @MessagePattern('find_one_product')
  findOne(@Payload() data: { merchantId: string; id: string }) {
    return this.productService.findOne(data.merchantId, data.id);
  }

  @MessagePattern(TOPICS.PRODUCT_UPDATED)
  update(@Payload() data: { merchantId: string; id: string; product: UpdateProductDto }) {
    return this.productService.update(data.merchantId, data.id, data.product);
  }

  @MessagePattern(TOPICS.INVENTORY_UPDATED)
  updateInventory(@Payload() data: { merchantId: string; id: string; inventory: UpdateInventoryDto }) {
    return this.productService.updateInventory(data.merchantId, data.id, data.inventory);
  }

  @MessagePattern(TOPICS.PRODUCT_DELETED)
  remove(@Payload() data: { merchantId: string; id: string }) {
    return this.productService.remove(data.merchantId, data.id);
  }

  @MessagePattern('get_product_categories')
  getCategories(@Payload() data: { merchantId: string }) {
    return this.productService.getCategories(data.merchantId);
  }
}

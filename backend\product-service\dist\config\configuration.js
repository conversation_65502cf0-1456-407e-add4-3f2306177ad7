"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = () => ({
    port: parseInt(process.env.PORT || '3002', 10),
    mongodb: {
        uri: process.env.MONGODB_URI || 'mongodb+srv://lesibamosese03:<EMAIL>/kqick_products?retryWrites=true&w=majority&appName=Cluster0',
    },
    redis: {
        host: process.env.REDIS_HOST || 'localhost',
        port: parseInt(process.env.REDIS_PORT || '6379', 10),
    },
    rabbitmq: {
        url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
        queue: 'product_queue',
    },
    auth: {
        jwtPublicKey: process.env.JWT_PUBLIC_KEY,
    },
});
//# sourceMappingURL=configuration.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.AnalyticsService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const rxjs_1 = require("rxjs");
const shared_lib_1 = require("../../../../../shared-lib/src");
let AnalyticsService = class AnalyticsService {
    constructor(productClient, merchantClient) {
        this.productClient = productClient;
        this.merchantClient = merchantClient;
    }
    async getDashboardAnalytics() {
        try {
            const [productStats, merchantStats] = await Promise.all([
                this.getProductStats(),
                this.getMerchantStats(),
            ]);
            return {
                products: productStats,
                merchants: merchantStats,
                summary: {
                    totalProducts: productStats.total,
                    totalMerchants: merchantStats.total,
                    activeMerchants: merchantStats.active,
                    pendingMerchants: merchantStats.pending,
                },
            };
        }
        catch (error) {
            return {
                products: { total: 0, active: 0, categories: 0 },
                merchants: { total: 0, active: 0, pending: 0 },
                summary: {
                    totalProducts: 0,
                    totalMerchants: 0,
                    activeMerchants: 0,
                    pendingMerchants: 0,
                },
            };
        }
    }
    async getProductAnalytics(startDate, endDate) {
        try {
            return await (0, rxjs_1.firstValueFrom)(this.productClient.send('get_product_analytics', { startDate, endDate }));
        }
        catch (error) {
            return {
                totalProducts: 0,
                newProducts: 0,
                categoriesBreakdown: [],
                brandsBreakdown: [],
            };
        }
    }
    async getMerchantAnalytics(startDate, endDate) {
        try {
            return await (0, rxjs_1.firstValueFrom)(this.merchantClient.send('get_merchant_analytics', { startDate, endDate }));
        }
        catch (error) {
            return {
                totalMerchants: 0,
                newMerchants: 0,
                activeMerchants: 0,
                statusBreakdown: [],
            };
        }
    }
    async getSalesAnalytics(startDate, endDate, groupBy = 'day') {
        try {
            return await (0, rxjs_1.firstValueFrom)(this.merchantClient.send('get_sales_analytics', { startDate, endDate, groupBy }));
        }
        catch (error) {
            return {
                totalSales: 0,
                salesData: [],
                growth: 0,
            };
        }
    }
    async getTopProducts(limit = 10, period) {
        try {
            return await (0, rxjs_1.firstValueFrom)(this.productClient.send('get_top_products', { limit, period }));
        }
        catch (error) {
            return [];
        }
    }
    async getTopMerchants(limit = 10, period) {
        try {
            return await (0, rxjs_1.firstValueFrom)(this.merchantClient.send('get_top_merchants', { limit, period }));
        }
        catch (error) {
            return [];
        }
    }
    async getProductStats() {
        try {
            const [totalProducts, categories] = await Promise.all([
                (0, rxjs_1.firstValueFrom)(this.productClient.send('find_all_universal_products', { page: 1, limit: 1 })),
                (0, rxjs_1.firstValueFrom)(this.productClient.send('get_universal_product_categories', {})),
            ]);
            return {
                total: totalProducts.total || 0,
                active: totalProducts.data?.filter((p) => p.isActive).length || 0,
                categories: categories.length || 0,
            };
        }
        catch (error) {
            return { total: 0, active: 0, categories: 0 };
        }
    }
    async getMerchantStats() {
        try {
            const merchants = await (0, rxjs_1.firstValueFrom)(this.merchantClient.send('find_all_merchants', { page: 1, limit: 1000 }));
            const total = merchants.total || 0;
            const active = merchants.data?.filter((m) => m.status === 'active').length || 0;
            const pending = merchants.data?.filter((m) => m.status === 'pending').length || 0;
            return { total, active, pending };
        }
        catch (error) {
            return { total: 0, active: 0, pending: 0 };
        }
    }
};
exports.AnalyticsService = AnalyticsService;
exports.AnalyticsService = AnalyticsService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(shared_lib_1.SERVICES.PRODUCT)),
    __param(1, (0, common_1.Inject)(shared_lib_1.SERVICES.MERCHANT)),
    __metadata("design:paramtypes", [microservices_1.ClientProxy,
        microservices_1.ClientProxy])
], AnalyticsService);
//# sourceMappingURL=analytics.service.js.map
import { Model } from 'mongoose';
import { Product } from './schemas/product.schema';
import { CreateProductDto, UpdateProductDto, UpdateInventoryDto } from './dto/product.dto';
import { SearchQuery, PaginatedResponse } from '../../types/shared';
export declare class ProductService {
    private readonly productModel;
    constructor(productModel: Model<Product>);
    create(merchantId: string, createProductDto: CreateProductDto): Promise<Product>;
    findAll(merchantId: string, query: SearchQuery): Promise<PaginatedResponse<Product>>;
    findOne(merchantId: string, id: string): Promise<Product>;
    update(merchantId: string, id: string, updateProductDto: UpdateProductDto): Promise<Product>;
    updateInventory(merchantId: string, id: string, updateInventoryDto: UpdateInventoryDto): Promise<Product>;
    remove(merchantId: string, id: string): Promise<void>;
    getCategories(merchantId: string): Promise<string[]>;
}

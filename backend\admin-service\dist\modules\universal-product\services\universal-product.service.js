"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalProductService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const rxjs_1 = require("rxjs");
const shared_1 = require("../../../constants/shared");
let UniversalProductService = class UniversalProductService {
    constructor(productClient) {
        this.productClient = productClient;
    }
    async createProduct(createDto) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send(shared_1.TOPICS.UNIVERSAL_PRODUCT_CREATED, createDto));
    }
    async findAll(searchDto) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('find_all_universal_products', searchDto));
    }
    async findById(id) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('find_universal_product_by_id', { id }));
    }
    async updateProduct(id, updateDto) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send(shared_1.TOPICS.UNIVERSAL_PRODUCT_UPDATED, { id, updateDto }));
    }
    async deleteProduct(id) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send(shared_1.TOPICS.UNIVERSAL_PRODUCT_DELETED, { id }));
    }
    async getCategories() {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('get_universal_product_categories', {}));
    }
    async getSubcategories(category) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('get_universal_product_subcategories', { category }));
    }
    async getBrands(category, subcategory) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('get_universal_product_brands', { category, subcategory }));
    }
    async searchByBarcode(barcode) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('search_universal_product_by_barcode', { barcode }));
    }
    async searchBySku(sku) {
        return (0, rxjs_1.firstValueFrom)(this.productClient.send('search_universal_product_by_sku', { sku }));
    }
    async bulkImport(products) {
        const results = [];
        for (const product of products) {
            try {
                const result = await this.createProduct(product);
                results.push({ success: true, product: result });
            }
            catch (error) {
                results.push({ success: false, error: error.message, product });
            }
        }
        return {
            total: products.length,
            successful: results.filter(r => r.success).length,
            failed: results.filter(r => !r.success).length,
            results,
        };
    }
    async getProductAnalytics() {
        const [categories, totalProducts] = await Promise.all([
            this.getCategories(),
            this.findAll({ page: 1, limit: 1 }),
        ]);
        return {
            totalProducts: totalProducts.total || 0,
            totalCategories: categories.length,
        };
    }
};
exports.UniversalProductService = UniversalProductService;
exports.UniversalProductService = UniversalProductService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(shared_1.SERVICES.PRODUCT)),
    __metadata("design:paramtypes", [microservices_1.ClientProxy])
], UniversalProductService);
//# sourceMappingURL=universal-product.service.js.map
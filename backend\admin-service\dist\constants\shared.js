"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOPICS = exports.SERVICES = void 0;
exports.SERVICES = {
    ADMIN_SERVICE: 'admin-service',
    PRODUCT_SERVICE: 'product-service',
    PRODUCT: 'product-service',
    MERCHANT_SERVICE: 'merchant-service',
    MERCHANT: 'merchant-service',
    AUTH_SERVICE: 'auth-service',
    ANALYTICS_SERVICE: 'analytics-service',
};
exports.TOPICS = {
    ADMIN_CREATED: 'admin.created',
    ADMIN_UPDATED: 'admin.updated',
    ADMIN_DELETED: 'admin.deleted',
    MERCHANT_CREATED: 'merchant.created',
    MERCHANT_UPDATED: 'merchant.updated',
    MERCHANT_DELETED: 'merchant.deleted',
    PRODUCT_CREATED: 'product.created',
    PRODUCT_UPDATED: 'product.updated',
    PRODUCT_DELETED: 'product.deleted',
    UNIVERSAL_PRODUCT_CREATED: 'universal.product.created',
    UNIVERSAL_PRODUCT_UPDATED: 'universal.product.updated',
    UNIVERSAL_PRODUCT_DELETED: 'universal.product.deleted',
};
//# sourceMappingURL=shared.js.map
"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantModule = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const config_1 = require("@nestjs/config");
const merchant_controller_1 = require("./controllers/merchant.controller");
const merchant_service_1 = require("./services/merchant.service");
const admin_module_1 = require("../admin/admin.module");
const shared_lib_1 = require("../../../../shared-lib/src");
let MerchantModule = class MerchantModule {
};
exports.MerchantModule = MerchantModule;
exports.MerchantModule = MerchantModule = __decorate([
    (0, common_1.Module)({
        imports: [
            admin_module_1.AdminModule,
            microservices_1.ClientsModule.registerAsync([
                {
                    name: shared_lib_1.SERVICES.MERCHANT,
                    imports: [config_1.ConfigModule],
                    useFactory: async (configService) => ({
                        transport: microservices_1.Transport.RMQ,
                        options: {
                            urls: [configService.get('rabbitmq.url')],
                            queue: 'merchant_queue',
                            queueOptions: {
                                durable: true,
                            },
                        },
                    }),
                    inject: [config_1.ConfigService],
                },
            ]),
        ],
        controllers: [merchant_controller_1.MerchantController],
        providers: [merchant_service_1.MerchantService],
        exports: [merchant_service_1.MerchantService],
    })
], MerchantModule);
//# sourceMappingURL=merchant.module.js.map
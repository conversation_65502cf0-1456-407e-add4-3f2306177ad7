import 'package:kqick_merchant/core/network/api_client.dart';
import 'package:kqick_merchant/shared/models/product.dart';

class ProductRepository {
  final ApiClient _apiClient;

  ProductRepository({required ApiClient apiClient}) : _apiClient = apiClient;

  Future<List<Product>> getProducts({
    int page = 1,
    int limit = 20,
    String? category,
    String? search,
  }) async {
    try {
      final response = await _apiClient.getProducts(
        page: page,
        limit: limit,
        category: category,
        search: search,
      );

      final List<dynamic> productsJson = response.data['data'];
      return productsJson.map((json) => Product.fromJson(json)).toList();
    } catch (e) {
      throw Exception('Failed to fetch products: $e');
    }
  }

  Future<Product> getProduct(String id) async {
    try {
      final response = await _apiClient.getProduct(id);
      return Product.fromJson(response.data);
    } catch (e) {
      throw Exception('Failed to fetch product: $e');
    }
  }

  Future<void> updateInventory(
    String productId, {
    required int quantity,
    required double price,
    bool? isAvailable,
  }) async {
    try {
      await _apiClient.updateInventory(productId, {
        'quantity': quantity,
        'price': price,
        if (isAvailable != null) 'isAvailable': isAvailable,
      });
    } catch (e) {
      throw Exception('Failed to update inventory: $e');
    }
  }
}

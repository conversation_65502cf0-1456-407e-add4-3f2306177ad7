import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Schema as MongooseSchema } from 'mongoose';
import { ProductVariant } from '../../../types/shared';

@Schema({ timestamps: true })
export class Product extends Document {
  @Prop({ required: true })
  name: string;

  @Prop({ required: true })
  description: string;

  @Prop({ required: true })
  category: string;

  @Prop({ required: true })
  basePrice: number;

  @Prop()
  imageUrl?: string;

  @Prop()
  barcode?: string;

  @Prop({ type: MongooseSchema.Types.Mixed })
  variants?: { [key: string]: ProductVariant };

  @Prop({ default: true })
  isAvailable: boolean;

  @Prop({ required: true, type: String })
  merchantId: string;
}

export const ProductSchema = SchemaFactory.createForClass(Product);

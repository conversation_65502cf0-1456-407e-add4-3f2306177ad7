import 'package:equatable/equatable.dart';
import 'package:kqick_merchant/shared/models/universal_product.dart';

abstract class UniversalCatalogState extends Equatable {
  const UniversalCatalogState();

  @override
  List<Object?> get props => [];
}

class UniversalCatalogInitial extends UniversalCatalogState {}

class UniversalCatalogLoading extends UniversalCatalogState {}

class UniversalCatalogError extends UniversalCatalogState {
  final String message;

  const UniversalCatalogError(this.message);

  @override
  List<Object?> get props => [message];
}

class UniversalProductsLoaded extends UniversalCatalogState {
  final List<UniversalProduct> products;
  final bool hasReachedMax;
  final int currentPage;
  final String? search;
  final String? category;
  final String? subcategory;
  final String? brand;
  final List<String>? tags;
  final double? minPrice;
  final double? maxPrice;
  final String? sortBy;
  final String? sortOrder;

  const UniversalProductsLoaded({
    required this.products,
    required this.hasReachedMax,
    required this.currentPage,
    this.search,
    this.category,
    this.subcategory,
    this.brand,
    this.tags,
    this.minPrice,
    this.maxPrice,
    this.sortBy,
    this.sortOrder,
  });

  @override
  List<Object?> get props => [
        products,
        hasReachedMax,
        currentPage,
        search,
        category,
        subcategory,
        brand,
        tags,
        minPrice,
        maxPrice,
        sortBy,
        sortOrder,
      ];

  UniversalProductsLoaded copyWith({
    List<UniversalProduct>? products,
    bool? hasReachedMax,
    int? currentPage,
    String? search,
    String? category,
    String? subcategory,
    String? brand,
    List<String>? tags,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
  }) {
    return UniversalProductsLoaded(
      products: products ?? this.products,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      search: search ?? this.search,
      category: category ?? this.category,
      subcategory: subcategory ?? this.subcategory,
      brand: brand ?? this.brand,
      tags: tags ?? this.tags,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }
}

class MerchantInventoryLoaded extends UniversalCatalogState {
  final List<MerchantInventoryWithProduct> inventoryItems;
  final bool hasReachedMax;
  final int currentPage;
  final String? search;
  final String? category;

  const MerchantInventoryLoaded({
    required this.inventoryItems,
    required this.hasReachedMax,
    required this.currentPage,
    this.search,
    this.category,
  });

  @override
  List<Object?> get props => [
        inventoryItems,
        hasReachedMax,
        currentPage,
        search,
        category,
      ];

  MerchantInventoryLoaded copyWith({
    List<MerchantInventoryWithProduct>? inventoryItems,
    bool? hasReachedMax,
    int? currentPage,
    String? search,
    String? category,
  }) {
    return MerchantInventoryLoaded(
      inventoryItems: inventoryItems ?? this.inventoryItems,
      hasReachedMax: hasReachedMax ?? this.hasReachedMax,
      currentPage: currentPage ?? this.currentPage,
      search: search ?? this.search,
      category: category ?? this.category,
    );
  }
}

class UniversalProductFound extends UniversalCatalogState {
  final UniversalProduct product;

  const UniversalProductFound(this.product);

  @override
  List<Object?> get props => [product];
}

class UniversalProductNotFound extends UniversalCatalogState {
  final String searchTerm;

  const UniversalProductNotFound(this.searchTerm);

  @override
  List<Object?> get props => [searchTerm];
}

class ProductAddedToInventory extends UniversalCatalogState {
  final MerchantInventory inventory;

  const ProductAddedToInventory(this.inventory);

  @override
  List<Object?> get props => [inventory];
}

class InventoryUpdated extends UniversalCatalogState {
  final MerchantInventory inventory;

  const InventoryUpdated(this.inventory);

  @override
  List<Object?> get props => [inventory];
}

class InventoryRemoved extends UniversalCatalogState {
  final String inventoryId;

  const InventoryRemoved(this.inventoryId);

  @override
  List<Object?> get props => [inventoryId];
}

class LowStockItemsLoaded extends UniversalCatalogState {
  final List<MerchantInventoryWithProduct> lowStockItems;

  const LowStockItemsLoaded(this.lowStockItems);

  @override
  List<Object?> get props => [lowStockItems];
}

class CategoriesLoaded extends UniversalCatalogState {
  final List<String> categories;

  const CategoriesLoaded(this.categories);

  @override
  List<Object?> get props => [categories];
}

class SubcategoriesLoaded extends UniversalCatalogState {
  final List<String> subcategories;
  final String? category;

  const SubcategoriesLoaded(this.subcategories, this.category);

  @override
  List<Object?> get props => [subcategories, category];
}

class BrandsLoaded extends UniversalCatalogState {
  final List<String> brands;
  final String? category;
  final String? subcategory;

  const BrandsLoaded(this.brands, this.category, this.subcategory);

  @override
  List<Object?> get props => [brands, category, subcategory];
}

@echo off
echo 🚀 Starting KQICK Super App Services...
echo.

echo 📦 Starting Product Service...
start "Product Service" cmd /k "cd backend\product-service && npm run start:dev"
timeout /t 3 /nobreak >nul

echo 🔧 Starting Admin Service...
start "Admin Service" cmd /k "cd backend\admin-service && npm run start:dev"
timeout /t 3 /nobreak >nul

echo 🌐 Starting Admin Dashboard...
start "Admin Dashboard" cmd /k "cd admin-dashboard && npm start"
timeout /t 3 /nobreak >nul

echo.
echo ✅ All services are starting up!
echo.
echo 📱 To start the merchant app, run:
echo    cd merchant_app
echo    flutter run
echo.
echo 🔗 Access points:
echo    Admin Dashboard: http://localhost:3000
echo    Product API: http://localhost:3002
echo    Admin API: http://localhost:3004
echo.
echo 🔐 Default admin login:
echo    Email: <EMAIL>
echo    Password: KqickAdmin123!
echo.
pause

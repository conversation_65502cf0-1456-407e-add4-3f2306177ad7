import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kqick_merchant/features/universal_catalog/data/repositories/universal_catalog_repository.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_event.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_state.dart';

class UniversalCatalogBloc extends Bloc<UniversalCatalogEvent, UniversalCatalogState> {
  final UniversalCatalogRepository _repository;
  static const int _productsPerPage = 20;

  UniversalCatalogBloc({required UniversalCatalogRepository repository})
      : _repository = repository,
        super(UniversalCatalogInitial()) {
    on<LoadUniversalProducts>(_onLoadUniversalProducts);
    on<LoadMoreUniversalProducts>(_onLoadMoreUniversalProducts);
    on<SearchUniversalProductByBarcode>(_onSearchByBarcode);
    on<SearchUniversalProductBySku>(_onSearchBySku);
    on<AddProductToInventory>(_onAddProductToInventory);
    on<LoadMerchantInventory>(_onLoadMerchantInventory);
    on<LoadMoreMerchantInventory>(_onLoadMoreMerchantInventory);
    on<UpdateMerchantInventory>(_onUpdateMerchantInventory);
    on<RemoveMerchantInventory>(_onRemoveMerchantInventory);
    on<UpdateStock>(_onUpdateStock);
    on<LoadLowStockItems>(_onLoadLowStockItems);
    on<LoadCategories>(_onLoadCategories);
    on<LoadSubcategories>(_onLoadSubcategories);
    on<LoadBrands>(_onLoadBrands);
  }

  Future<void> _onLoadUniversalProducts(
    LoadUniversalProducts event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      emit(UniversalCatalogLoading());

      final products = await _repository.getUniversalProducts(
        page: 1,
        limit: _productsPerPage,
        search: event.search,
        category: event.category,
        subcategory: event.subcategory,
        brand: event.brand,
        tags: event.tags,
        minPrice: event.minPrice,
        maxPrice: event.maxPrice,
        sortBy: event.sortBy,
        sortOrder: event.sortOrder,
      );

      emit(UniversalProductsLoaded(
        products: products,
        hasReachedMax: products.length < _productsPerPage,
        currentPage: 1,
        search: event.search,
        category: event.category,
        subcategory: event.subcategory,
        brand: event.brand,
        tags: event.tags,
        minPrice: event.minPrice,
        maxPrice: event.maxPrice,
        sortBy: event.sortBy,
        sortOrder: event.sortOrder,
      ));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadMoreUniversalProducts(
    LoadMoreUniversalProducts event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    final currentState = state;
    if (currentState is! UniversalProductsLoaded || currentState.hasReachedMax) {
      return;
    }

    try {
      final nextPage = currentState.currentPage + 1;
      final moreProducts = await _repository.getUniversalProducts(
        page: nextPage,
        limit: _productsPerPage,
        search: currentState.search,
        category: currentState.category,
        subcategory: currentState.subcategory,
        brand: currentState.brand,
        tags: currentState.tags,
        minPrice: currentState.minPrice,
        maxPrice: currentState.maxPrice,
        sortBy: currentState.sortBy,
        sortOrder: currentState.sortOrder,
      );

      emit(moreProducts.isEmpty
          ? currentState.copyWith(hasReachedMax: true)
          : UniversalProductsLoaded(
              products: [...currentState.products, ...moreProducts],
              hasReachedMax: moreProducts.length < _productsPerPage,
              currentPage: nextPage,
              search: currentState.search,
              category: currentState.category,
              subcategory: currentState.subcategory,
              brand: currentState.brand,
              tags: currentState.tags,
              minPrice: currentState.minPrice,
              maxPrice: currentState.maxPrice,
              sortBy: currentState.sortBy,
              sortOrder: currentState.sortOrder,
            ));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onSearchByBarcode(
    SearchUniversalProductByBarcode event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      emit(UniversalCatalogLoading());
      final product = await _repository.searchByBarcode(event.barcode);
      
      if (product != null) {
        emit(UniversalProductFound(product));
      } else {
        emit(UniversalProductNotFound(event.barcode));
      }
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onSearchBySku(
    SearchUniversalProductBySku event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      emit(UniversalCatalogLoading());
      final product = await _repository.searchBySku(event.sku);
      
      if (product != null) {
        emit(UniversalProductFound(product));
      } else {
        emit(UniversalProductNotFound(event.sku));
      }
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onAddProductToInventory(
    AddProductToInventory event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final inventory = await _repository.addProductToInventory(
        universalProductId: event.universalProductId,
        price: event.price,
        stock: event.stock,
        isAvailable: event.isAvailable,
        minStock: event.minStock,
        maxStock: event.maxStock,
        location: event.location,
      );

      emit(ProductAddedToInventory(inventory));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadMerchantInventory(
    LoadMerchantInventory event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      emit(UniversalCatalogLoading());

      final inventoryItems = await _repository.getMerchantInventory(
        page: 1,
        limit: _productsPerPage,
        search: event.search,
        category: event.category,
      );

      emit(MerchantInventoryLoaded(
        inventoryItems: inventoryItems,
        hasReachedMax: inventoryItems.length < _productsPerPage,
        currentPage: 1,
        search: event.search,
        category: event.category,
      ));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadMoreMerchantInventory(
    LoadMoreMerchantInventory event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    final currentState = state;
    if (currentState is! MerchantInventoryLoaded || currentState.hasReachedMax) {
      return;
    }

    try {
      final nextPage = currentState.currentPage + 1;
      final moreItems = await _repository.getMerchantInventory(
        page: nextPage,
        limit: _productsPerPage,
        search: currentState.search,
        category: currentState.category,
      );

      emit(moreItems.isEmpty
          ? currentState.copyWith(hasReachedMax: true)
          : MerchantInventoryLoaded(
              inventoryItems: [...currentState.inventoryItems, ...moreItems],
              hasReachedMax: moreItems.length < _productsPerPage,
              currentPage: nextPage,
              search: currentState.search,
              category: currentState.category,
            ));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onUpdateMerchantInventory(
    UpdateMerchantInventory event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final inventory = await _repository.updateMerchantInventory(
        inventoryId: event.inventoryId,
        price: event.price,
        stock: event.stock,
        isAvailable: event.isAvailable,
        minStock: event.minStock,
        maxStock: event.maxStock,
        location: event.location,
      );

      emit(InventoryUpdated(inventory));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onRemoveMerchantInventory(
    RemoveMerchantInventory event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      await _repository.removeMerchantInventory(event.inventoryId);
      emit(InventoryRemoved(event.inventoryId));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onUpdateStock(
    UpdateStock event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final inventory = await _repository.updateStock(
        event.universalProductId,
        event.stockChange,
      );
      emit(InventoryUpdated(inventory));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadLowStockItems(
    LoadLowStockItems event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      emit(UniversalCatalogLoading());
      final lowStockItems = await _repository.getLowStockItems();
      emit(LowStockItemsLoaded(lowStockItems));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadCategories(
    LoadCategories event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final categories = await _repository.getCategories();
      emit(CategoriesLoaded(categories));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadSubcategories(
    LoadSubcategories event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final subcategories = await _repository.getSubcategories(event.category);
      emit(SubcategoriesLoaded(subcategories, event.category));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }

  Future<void> _onLoadBrands(
    LoadBrands event,
    Emitter<UniversalCatalogState> emit,
  ) async {
    try {
      final brands = await _repository.getBrands(event.category, event.subcategory);
      emit(BrandsLoaded(brands, event.category, event.subcategory));
    } catch (e) {
      emit(UniversalCatalogError(e.toString()));
    }
  }
}

import React, { useEffect } from 'react';
import { Navigate } from 'react-router-dom';
import { useSelector, useDispatch } from 'react-redux';
import { Box, CircularProgress } from '@mui/material';
import { RootState } from '../../store/store';
import { loginSuccess, logout } from '../../store/slices/authSlice';
import { apiService } from '../../services/api';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const dispatch = useDispatch();
  const { isAuthenticated, accessToken, loading } = useSelector((state: RootState) => state.auth);
  const [isValidating, setIsValidating] = React.useState(true);

  useEffect(() => {
    const validateToken = async () => {
      if (accessToken && !isAuthenticated) {
        try {
          const profile = await apiService.getProfile();
          dispatch(loginSuccess({
            admin: profile,
            accessToken,
            refreshToken: localStorage.getItem('refreshToken') || '',
          }));
        } catch (error) {
          dispatch(logout());
        }
      }
      setIsValidating(false);
    };

    validateToken();
  }, [accessToken, isAuthenticated, dispatch]);

  if (isValidating || loading) {
    return (
      <Box
        display="flex"
        justifyContent="center"
        alignItems="center"
        minHeight="100vh"
      >
        <CircularProgress />
      </Box>
    );
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;

export default () => ({
  port: parseInt(process.env.PORT, 10) || 3004,
  mongodb: {
    uri: process.env.MONGODB_URI || 'mongodb://localhost:27017/kqick_admin',
  },
  rabbitmq: {
    url: process.env.RABBITMQ_URL || 'amqp://localhost:5672',
    queue: 'admin_queue',
  },
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },
  jwt: {
    secret: process.env.JWT_SECRET || 'admin-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h',
  },
});

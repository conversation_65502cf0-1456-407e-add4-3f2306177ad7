"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.UniversalCatalogModule = void 0;
const common_1 = require("@nestjs/common");
const mongoose_1 = require("@nestjs/mongoose");
const universal_catalog_service_1 = require("./services/universal-catalog.service");
const merchant_inventory_service_1 = require("./services/merchant-inventory.service");
const universal_catalog_controller_1 = require("./controllers/universal-catalog.controller");
const merchant_inventory_controller_1 = require("./controllers/merchant-inventory.controller");
const universal_product_schema_1 = require("./schemas/universal-product.schema");
const merchant_inventory_schema_1 = require("./schemas/merchant-inventory.schema");
let UniversalCatalogModule = class UniversalCatalogModule {
};
exports.UniversalCatalogModule = UniversalCatalogModule;
exports.UniversalCatalogModule = UniversalCatalogModule = __decorate([
    (0, common_1.Module)({
        imports: [
            mongoose_1.MongooseModule.forFeature([
                { name: universal_product_schema_1.UniversalProduct.name, schema: universal_product_schema_1.UniversalProductSchema },
                { name: merchant_inventory_schema_1.MerchantInventory.name, schema: merchant_inventory_schema_1.MerchantInventorySchema },
            ]),
        ],
        controllers: [universal_catalog_controller_1.UniversalCatalogController, merchant_inventory_controller_1.MerchantInventoryController],
        providers: [universal_catalog_service_1.UniversalCatalogService, merchant_inventory_service_1.MerchantInventoryService],
        exports: [universal_catalog_service_1.UniversalCatalogService, merchant_inventory_service_1.MerchantInventoryService],
    })
], UniversalCatalogModule);
//# sourceMappingURL=universal-catalog.module.js.map
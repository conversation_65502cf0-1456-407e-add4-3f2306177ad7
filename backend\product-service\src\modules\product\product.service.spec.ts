import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { ProductService } from './product.service';
import { Product } from './schemas/product.schema';
import { CreateProductDto } from './dto/product.dto';
import { NotFoundError } from '../../types/shared';

const mockProduct = {
  id: 'product-1',
  name: 'Test Product',
  description: 'Test Description',
  category: 'Test Category',
  basePrice: 100,
  merchantId: 'merchant-1',
  isAvailable: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

describe('ProductService', () => {
  let service: ProductService;
  let model: Model<Product>;

  const mockProductModel = {
    new: jest.fn().mockResolvedValue(mockProduct),
    constructor: jest.fn().mockResolvedValue(mockProduct),
    find: jest.fn(),
    findOne: jest.fn(),
    findOneAndUpdate: jest.fn(),
    deleteOne: jest.fn(),
    distinct: jest.fn(),
    save: jest.fn(),
    exec: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProductService,
        {
          provide: getModelToken(Product.name),
          useValue: mockProductModel,
        },
      ],
    }).compile();

    service = module.get<ProductService>(ProductService);
    model = module.get<Model<Product>>(getModelToken(Product.name));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a product', async () => {
      const createProductDto: CreateProductDto = {
        name: 'Test Product',
        description: 'Test Description',
        category: 'Test Category',
        basePrice: 100,
      };

      const merchantId = 'merchant-1';

      mockProductModel.save.mockResolvedValueOnce(mockProduct);

      const result = await service.create(merchantId, createProductDto);

      expect(result).toEqual(mockProduct);
      expect(mockProductModel.new).toHaveBeenCalledWith({
        ...createProductDto,
        merchantId,
      });
    });
  });

  describe('findOne', () => {
    it('should return a product', async () => {
      mockProductModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(mockProduct),
      });

      const result = await service.findOne('merchant-1', 'product-1');

      expect(result).toEqual(mockProduct);
      expect(mockProductModel.findOne).toHaveBeenCalledWith({
        _id: 'product-1',
        merchantId: 'merchant-1',
      });
    });

    it('should throw NotFoundError if product is not found', async () => {
      mockProductModel.findOne.mockReturnValue({
        exec: jest.fn().mockResolvedValueOnce(null),
      });

      await expect(service.findOne('merchant-1', 'product-1')).rejects.toThrow(
        NotFoundError,
      );
    });
  });
});

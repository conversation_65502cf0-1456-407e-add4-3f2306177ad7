import React from 'react';
import {
  Box,
  Grid,
  Paper,
  Typography,
  Card,
  CardContent,
  CircularProgress,
} from '@mui/material';
import {
  Inventory,
  Store,
  TrendingUp,
  People,
} from '@mui/icons-material';
import { useQuery } from 'react-query';
import { apiService } from '../../services/api';

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color }) => (
  <Card>
    <CardContent>
      <Box display="flex" alignItems="center" justifyContent="space-between">
        <Box>
          <Typography color="textSecondary" gutterBottom variant="body2">
            {title}
          </Typography>
          <Typography variant="h4" component="h2">
            {value}
          </Typography>
        </Box>
        <Box
          sx={{
            backgroundColor: color,
            borderRadius: '50%',
            width: 56,
            height: 56,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            color: 'white',
          }}
        >
          {icon}
        </Box>
      </Box>
    </CardContent>
  </Card>
);

const Dashboard: React.FC = () => {
  const { data: analytics, isLoading, error } = useQuery(
    'dashboardAnalytics',
    apiService.getDashboardAnalytics,
    {
      refetchInterval: 30000, // Refetch every 30 seconds
    }
  );

  if (isLoading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box>
        <Typography variant="h6" color="error">
          Error loading dashboard data
        </Typography>
      </Box>
    );
  }

  const stats = [
    {
      title: 'Total Products',
      value: analytics?.summary?.totalProducts || 0,
      icon: <Inventory />,
      color: '#1976d2',
    },
    {
      title: 'Total Merchants',
      value: analytics?.summary?.totalMerchants || 0,
      icon: <Store />,
      color: '#388e3c',
    },
    {
      title: 'Active Merchants',
      value: analytics?.summary?.activeMerchants || 0,
      icon: <People />,
      color: '#f57c00',
    },
    {
      title: 'Pending Approvals',
      value: analytics?.summary?.pendingMerchants || 0,
      icon: <TrendingUp />,
      color: '#d32f2f',
    },
  ];

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Dashboard
      </Typography>
      
      <Grid container spacing={3}>
        {stats.map((stat, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <StatCard {...stat} />
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3} sx={{ mt: 3 }}>
        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Product Overview
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="textSecondary">
                Total Products: {analytics?.products?.total || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Products: {analytics?.products?.active || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Categories: {analytics?.products?.categories || 0}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={6}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Merchant Overview
            </Typography>
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="textSecondary">
                Total Merchants: {analytics?.merchants?.total || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Active Merchants: {analytics?.merchants?.active || 0}
              </Typography>
              <Typography variant="body2" color="textSecondary">
                Pending Approval: {analytics?.merchants?.pending || 0}
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Recent Activity
            </Typography>
            <Typography variant="body2" color="textSecondary">
              Activity feed will be implemented here...
            </Typography>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default Dashboard;

import axios, { AxiosInstance, AxiosRequestConfig } from 'axios';
import { store } from '../store/store';
import { logout } from '../store/slices/authSlice';

class ApiService {
  private api: AxiosInstance;

  constructor() {
    this.api = axios.create({
      baseURL: process.env.REACT_APP_API_URL || 'http://localhost:3004',
      timeout: 10000,
    });

    this.setupInterceptors();
  }

  private setupInterceptors() {
    // Request interceptor to add auth token
    this.api.interceptors.request.use(
      (config) => {
        const state = store.getState();
        const token = state.auth.accessToken;
        
        if (token) {
          config.headers.Authorization = `Bearer ${token}`;
        }
        
        return config;
      },
      (error) => {
        return Promise.reject(error);
      }
    );

    // Response interceptor to handle token refresh
    this.api.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            const state = store.getState();
            const refreshToken = state.auth.refreshToken;

            if (refreshToken) {
              const response = await this.api.post('/admin/refresh', {
                refreshToken,
              });

              const { accessToken, refreshToken: newRefreshToken } = response.data;
              
              // Update tokens in store
              store.dispatch({
                type: 'auth/loginSuccess',
                payload: {
                  admin: state.auth.admin,
                  accessToken,
                  refreshToken: newRefreshToken,
                },
              });

              // Retry original request
              originalRequest.headers.Authorization = `Bearer ${accessToken}`;
              return this.api(originalRequest);
            }
          } catch (refreshError) {
            store.dispatch(logout());
            window.location.href = '/login';
          }
        }

        return Promise.reject(error);
      }
    );
  }

  // Auth endpoints
  async login(email: string, password: string) {
    const response = await this.api.post('/admin/login', { email, password });
    return response.data;
  }

  async logout() {
    await this.api.post('/admin/logout');
  }

  async getProfile() {
    const response = await this.api.get('/admin/profile');
    return response.data;
  }

  async updateProfile(data: any) {
    const response = await this.api.put('/admin/profile', data);
    return response.data;
  }

  async changePassword(currentPassword: string, newPassword: string) {
    await this.api.put('/admin/profile/password', {
      currentPassword,
      newPassword,
    });
  }

  // Universal Products endpoints
  async getUniversalProducts(params?: any) {
    const response = await this.api.get('/universal-products', { params });
    return response.data;
  }

  async getUniversalProduct(id: string) {
    const response = await this.api.get(`/universal-products/${id}`);
    return response.data;
  }

  async createUniversalProduct(data: any) {
    const response = await this.api.post('/universal-products', data);
    return response.data;
  }

  async updateUniversalProduct(id: string, data: any) {
    const response = await this.api.put(`/universal-products/${id}`, data);
    return response.data;
  }

  async deleteUniversalProduct(id: string) {
    await this.api.delete(`/universal-products/${id}`);
  }

  async getProductCategories() {
    const response = await this.api.get('/universal-products/categories');
    return response.data;
  }

  async getProductSubcategories(category?: string) {
    const response = await this.api.get('/universal-products/subcategories', {
      params: { category },
    });
    return response.data;
  }

  async getProductBrands(category?: string, subcategory?: string) {
    const response = await this.api.get('/universal-products/brands', {
      params: { category, subcategory },
    });
    return response.data;
  }

  // Merchants endpoints
  async getMerchants(params?: any) {
    const response = await this.api.get('/merchants', { params });
    return response.data;
  }

  async getMerchant(id: string) {
    const response = await this.api.get(`/merchants/${id}`);
    return response.data;
  }

  async approveMerchant(id: string) {
    const response = await this.api.put(`/merchants/${id}/approve`);
    return response.data;
  }

  async rejectMerchant(id: string) {
    const response = await this.api.put(`/merchants/${id}/reject`);
    return response.data;
  }

  async suspendMerchant(id: string) {
    const response = await this.api.put(`/merchants/${id}/suspend`);
    return response.data;
  }

  async activateMerchant(id: string) {
    const response = await this.api.put(`/merchants/${id}/activate`);
    return response.data;
  }

  // Analytics endpoints
  async getDashboardAnalytics() {
    const response = await this.api.get('/analytics/dashboard');
    return response.data;
  }

  async getProductAnalytics(startDate?: string, endDate?: string) {
    const response = await this.api.get('/analytics/products', {
      params: { startDate, endDate },
    });
    return response.data;
  }

  async getMerchantAnalytics(startDate?: string, endDate?: string) {
    const response = await this.api.get('/analytics/merchants', {
      params: { startDate, endDate },
    });
    return response.data;
  }

  // Admin management endpoints
  async getAdmins() {
    const response = await this.api.get('/admin');
    return response.data;
  }

  async createAdmin(data: any) {
    const response = await this.api.post('/admin', data);
    return response.data;
  }

  async updateAdmin(id: string, data: any) {
    const response = await this.api.put(`/admin/${id}`, data);
    return response.data;
  }

  async deleteAdmin(id: string) {
    await this.api.delete(`/admin/${id}`);
  }
}

export const apiService = new ApiService();

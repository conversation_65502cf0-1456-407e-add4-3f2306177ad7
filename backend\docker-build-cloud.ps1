# KQICK Docker Build Cloud Script (PowerShell)
# This script builds all microservices using Docker Build Cloud

param(
    [string]$Tag = "latest"
)

# Configuration
$BuilderName = "kqick-cloud-builder"
$Registry = "kqick00"

Write-Host "🚀 KQICK Docker Build Cloud Setup" -ForegroundColor Green
Write-Host "==================================" -ForegroundColor Green

# Check if Docker is available
try {
    docker --version | Out-Null
} catch {
    Write-Host "❌ Docker is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Check if buildx is available
try {
    docker buildx version | Out-Null
} catch {
    Write-Host "❌ Docker Buildx is not available" -ForegroundColor Red
    exit 1
}

# Create cloud builder if it doesn't exist
Write-Host "🔧 Setting up cloud builder..." -ForegroundColor Yellow
$builderExists = docker buildx ls | Select-String $BuilderName
if (-not $builderExists) {
    Write-Host "Creating new cloud builder: $BuilderName" -ForegroundColor Cyan
    docker buildx create --driver cloud "$Registry/kqick" --name $BuilderName
} else {
    Write-Host "Cloud builder $BuilderName already exists" -ForegroundColor Green
}

# Use the cloud builder
Write-Host "🎯 Using cloud builder: $BuilderName" -ForegroundColor Yellow
docker buildx use $BuilderName

# Build services
Write-Host "🏗️  Building KQICK microservices..." -ForegroundColor Yellow

# Build shared library first
Write-Host "📦 Building shared library..." -ForegroundColor Cyan
Set-Location shared-lib
docker buildx build `
    --platform linux/amd64,linux/arm64 `
    --tag "$Registry/kqick-shared-lib:$Tag" `
    --push `
    .
Set-Location ..

# Build each service
$services = @("auth-service", "product-service", "admin-service", "api-gateway")

foreach ($service in $services) {
    if (Test-Path $service) {
        Write-Host "🔨 Building $service..." -ForegroundColor Cyan
        Set-Location $service
        
        # Build and push multi-platform image
        docker buildx build `
            --platform linux/amd64,linux/arm64 `
            --tag "$Registry/kqick-$service:$Tag" `
            --tag "$Registry/kqick-$service:latest" `
            --push `
            .
        
        Set-Location ..
        Write-Host "✅ $service built successfully" -ForegroundColor Green
    } else {
        Write-Host "⚠️  $service directory not found, skipping..." -ForegroundColor Yellow
    }
}

Write-Host ""
Write-Host "🎉 All KQICK services built successfully!" -ForegroundColor Green
Write-Host "📋 Built images:" -ForegroundColor Cyan
foreach ($service in $services) {
    Write-Host "   - $Registry/kqick-$service:$Tag" -ForegroundColor White
}
Write-Host "   - $Registry/kqick-shared-lib:$Tag" -ForegroundColor White
Write-Host ""
Write-Host "🚀 Ready to deploy with docker-compose or Kubernetes!" -ForegroundColor Green

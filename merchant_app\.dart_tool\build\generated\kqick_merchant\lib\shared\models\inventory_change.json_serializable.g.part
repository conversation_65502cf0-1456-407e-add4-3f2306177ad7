// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

InventoryChange _$InventoryChangeFromJson(Map<String, dynamic> json) =>
    InventoryChange(
      id: json['id'] as String,
      productId: json['productId'] as String,
      oldPrice: (json['oldPrice'] as num).toDouble(),
      newPrice: (json['newPrice'] as num).toDouble(),
      oldQuantity: (json['oldQuantity'] as num).toInt(),
      newQuantity: (json['newQuantity'] as num).toInt(),
      oldAvailability: json['oldAvailability'] as bool?,
      newAvailability: json['newAvailability'] as bool?,
      note: json['note'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );

Map<String, dynamic> _$InventoryChangeToJson(InventoryChange instance) =>
    <String, dynamic>{
      'id': instance.id,
      'productId': instance.productId,
      'oldPrice': instance.oldPrice,
      'newPrice': instance.newPrice,
      'oldQuantity': instance.oldQuantity,
      'newQuantity': instance.newQuantity,
      'oldAvailability': instance.oldAvailability,
      'newAvailability': instance.newAvailability,
      'note': instance.note,
      'createdAt': instance.createdAt.toIso8601String(),
    };

# KQICK Setup Script for Windows PowerShell
Write-Host "🚀 Setting up KQICK Super App..." -ForegroundColor Green

# Check if Node.js is installed
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js from https://nodejs.org/" -ForegroundColor Red
    exit 1
}

# Check if npm is installed
try {
    $npmVersion = npm --version
    Write-Host "✅ npm found: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ npm not found. Please install npm" -ForegroundColor Red
    exit 1
}

Write-Host "`n📦 Installing dependencies..." -ForegroundColor Yellow

# Install shared library dependencies
Write-Host "Installing shared library..." -ForegroundColor Cyan
Set-Location "backend/shared-lib"
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install shared library dependencies" -ForegroundColor Red
    exit 1
}

# Install product service dependencies
Write-Host "Installing product service..." -ForegroundColor Cyan
Set-Location "../product-service"
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install product service dependencies" -ForegroundColor Red
    exit 1
}

# Install admin service dependencies
Write-Host "Installing admin service..." -ForegroundColor Cyan
Set-Location "../admin-service"
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install admin service dependencies" -ForegroundColor Red
    exit 1
}

# Install merchant app dependencies
Write-Host "Installing merchant app..." -ForegroundColor Cyan
Set-Location "../../merchant_app"
flutter pub get
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install merchant app dependencies" -ForegroundColor Red
    exit 1
}

# Install admin dashboard dependencies
Write-Host "Installing admin dashboard..." -ForegroundColor Cyan
Set-Location "../admin-dashboard"
npm install
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ Failed to install admin dashboard dependencies" -ForegroundColor Red
    exit 1
}

# Go back to root
Set-Location ".."

Write-Host "`n✅ All dependencies installed successfully!" -ForegroundColor Green

Write-Host "`n🔧 Setting up environment files..." -ForegroundColor Yellow

# Create environment files
if (!(Test-Path "backend/.env")) {
    Copy-Item "backend/.env.example" "backend/.env"
    Write-Host "✅ Created backend/.env file" -ForegroundColor Green
} else {
    Write-Host "⚠️  backend/.env already exists" -ForegroundColor Yellow
}

if (!(Test-Path "admin-dashboard/.env")) {
    "REACT_APP_API_URL=http://localhost:3004" | Out-File -FilePath "admin-dashboard/.env" -Encoding UTF8
    Write-Host "✅ Created admin-dashboard/.env file" -ForegroundColor Green
} else {
    Write-Host "⚠️  admin-dashboard/.env already exists" -ForegroundColor Yellow
}

Write-Host "`n🎯 KQICK Setup Complete!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Start the product service: cd backend/product-service && npm run start:dev" -ForegroundColor White
Write-Host "2. Start the admin service: cd backend/admin-service && npm run start:dev" -ForegroundColor White
Write-Host "3. Start the admin dashboard: cd admin-dashboard && npm start" -ForegroundColor White
Write-Host "4. Start the merchant app: cd merchant_app && flutter run" -ForegroundColor White

Write-Host "`n🔐 Security Note:" -ForegroundColor Red
Write-Host "Please change your MongoDB credentials after setup!" -ForegroundColor Red
Write-Host "Update the connection strings in the configuration files." -ForegroundColor Red

Write-Host "`n📚 Documentation:" -ForegroundColor Cyan
Write-Host "- Admin System: See ADMIN_SYSTEM_README.md" -ForegroundColor White
Write-Host "- API Endpoints: Check individual service documentation" -ForegroundColor White

Write-Host "`n🌟 Happy coding with KQICK!" -ForegroundColor Green

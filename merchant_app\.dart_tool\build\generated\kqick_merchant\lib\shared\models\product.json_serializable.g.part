// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

Product _$ProductFromJson(Map<String, dynamic> json) => Product(
  id: json['id'] as String,
  name: json['name'] as String,
  description: json['description'] as String,
  category: json['category'] as String,
  basePrice: (json['basePrice'] as num).toDouble(),
  imageUrl: json['imageUrl'] as String?,
  barcode: json['barcode'] as String?,
  variants: json['variants'] as Map<String, dynamic>?,
  isAvailable: json['isAvailable'] as bool? ?? true,
  createdAt: DateTime.parse(json['createdAt'] as String),
  updatedAt: DateTime.parse(json['updatedAt'] as String),
);

Map<String, dynamic> _$ProductToJson(Product instance) => <String, dynamic>{
  'id': instance.id,
  'name': instance.name,
  'description': instance.description,
  'category': instance.category,
  'basePrice': instance.basePrice,
  'imageUrl': instance.imageUrl,
  'barcode': instance.barcode,
  'variants': instance.variants,
  'isAvailable': instance.isAvailable,
  'createdAt': instance.createdAt.toIso8601String(),
  'updatedAt': instance.updatedAt.toIso8601String(),
};

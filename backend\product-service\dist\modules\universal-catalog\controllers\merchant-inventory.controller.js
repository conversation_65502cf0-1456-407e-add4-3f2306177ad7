"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantInventoryController = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const merchant_inventory_service_1 = require("../services/merchant-inventory.service");
const shared_lib_1 = require("@kqick/shared-lib");
let MerchantInventoryController = class MerchantInventoryController {
    constructor(merchantInventoryService) {
        this.merchantInventoryService = merchantInventoryService;
    }
    addProductToInventory(data) {
        return this.merchantInventoryService.addProductToInventory(data.merchantId, data.createDto);
    }
    getMerchantInventory(data) {
        return this.merchantInventoryService.getMerchantInventory(data.merchantId, data.page, data.limit, data.search, data.category);
    }
    updateMerchantInventory(data) {
        return this.merchantInventoryService.updateMerchantInventory(data.merchantId, data.inventoryId, data.updateDto);
    }
    removeMerchantInventory(data) {
        return this.merchantInventoryService.removeMerchantInventory(data.merchantId, data.inventoryId);
    }
    getMerchantInventoryByProduct(data) {
        return this.merchantInventoryService.getMerchantInventoryByProduct(data.merchantId, data.universalProductId);
    }
    updateStock(data) {
        return this.merchantInventoryService.updateStock(data.merchantId, data.universalProductId, data.stockChange);
    }
    getLowStockItems(data) {
        return this.merchantInventoryService.getLowStockItems(data.merchantId);
    }
};
exports.MerchantInventoryController = MerchantInventoryController;
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.MERCHANT_INVENTORY_CREATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "addProductToInventory", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_merchant_inventory'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "getMerchantInventory", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.MERCHANT_INVENTORY_UPDATED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "updateMerchantInventory", null);
__decorate([
    (0, microservices_1.MessagePattern)(shared_lib_1.TOPICS.MERCHANT_INVENTORY_DELETED),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "removeMerchantInventory", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_merchant_inventory_by_product'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "getMerchantInventoryByProduct", null);
__decorate([
    (0, microservices_1.MessagePattern)('update_merchant_stock'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "updateStock", null);
__decorate([
    (0, microservices_1.MessagePattern)('get_low_stock_items'),
    __param(0, (0, microservices_1.Payload)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [Object]),
    __metadata("design:returntype", void 0)
], MerchantInventoryController.prototype, "getLowStockItems", null);
exports.MerchantInventoryController = MerchantInventoryController = __decorate([
    (0, common_1.Controller)(),
    __metadata("design:paramtypes", [merchant_inventory_service_1.MerchantInventoryService])
], MerchantInventoryController);
//# sourceMappingURL=merchant-inventory.controller.js.map
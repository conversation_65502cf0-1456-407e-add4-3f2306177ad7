import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { MerchantInventory } from '../schemas/merchant-inventory.schema';
import { UniversalProduct } from '../schemas/universal-product.schema';
import {
  CreateMerchantInventoryDto,
  UpdateMerchantInventoryDto,
} from '../dto/universal-product.dto';
import { NotFoundError, PaginatedResponse } from '@kqick/shared-lib';

@Injectable()
export class MerchantInventoryService {
  constructor(
    @InjectModel(MerchantInventory.name)
    private readonly merchantInventoryModel: Model<MerchantInventory>,
    @InjectModel(UniversalProduct.name)
    private readonly universalProductModel: Model<UniversalProduct>,
  ) {}

  async addProductToInventory(
    merchantId: string,
    createDto: CreateMerchantInventoryDto,
  ): Promise<MerchantInventory> {
    // Verify universal product exists
    const universalProduct = await this.universalProductModel.findById(
      createDto.universalProductId,
    );
    if (!universalProduct) {
      throw new NotFoundError('Universal product not found');
    }

    // Check if merchant already has this product in inventory
    const existingInventory = await this.merchantInventoryModel.findOne({
      merchantId,
      universalProductId: createDto.universalProductId,
    });

    if (existingInventory) {
      throw new Error('Product already exists in merchant inventory');
    }

    const inventory = new this.merchantInventoryModel({
      ...createDto,
      merchantId,
    });

    return inventory.save();
  }

  async getMerchantInventory(
    merchantId: string,
    page: number = 1,
    limit: number = 20,
    search?: string,
    category?: string,
  ): Promise<PaginatedResponse<any>> {
    const skip = (page - 1) * limit;

    // Build aggregation pipeline
    const pipeline: any[] = [
      { $match: { merchantId } },
      {
        $lookup: {
          from: 'universalproducts',
          localField: 'universalProductId',
          foreignField: '_id',
          as: 'product',
        },
      },
      { $unwind: '$product' },
      { $match: { 'product.isActive': true } },
    ];

    // Add search filter
    if (search) {
      pipeline.push({
        $match: {
          $or: [
            { 'product.name': { $regex: search, $options: 'i' } },
            { 'product.description': { $regex: search, $options: 'i' } },
            { 'product.brand': { $regex: search, $options: 'i' } },
          ],
        },
      });
    }

    // Add category filter
    if (category) {
      pipeline.push({
        $match: { 'product.category': category },
      });
    }

    // Add pagination
    const countPipeline = [...pipeline, { $count: 'total' }];
    pipeline.push({ $skip: skip }, { $limit: limit });

    // Add projection to format the response
    pipeline.push({
      $project: {
        _id: 1,
        merchantId: 1,
        price: 1,
        stock: 1,
        isAvailable: 1,
        minStock: 1,
        maxStock: 1,
        location: 1,
        createdAt: 1,
        updatedAt: 1,
        product: {
          _id: '$product._id',
          name: '$product.name',
          description: '$product.description',
          category: '$product.category',
          subcategory: '$product.subcategory',
          brand: '$product.brand',
          suggestedPrice: '$product.suggestedPrice',
          imageUrl: '$product.imageUrl',
          barcode: '$product.barcode',
          sku: '$product.sku',
          variants: '$product.variants',
          tags: '$product.tags',
          specifications: '$product.specifications',
        },
      },
    });

    const [inventoryItems, totalResult] = await Promise.all([
      this.merchantInventoryModel.aggregate(pipeline),
      this.merchantInventoryModel.aggregate(countPipeline),
    ]);

    const total = totalResult[0]?.total || 0;

    return {
      data: inventoryItems,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateMerchantInventory(
    merchantId: string,
    inventoryId: string,
    updateDto: UpdateMerchantInventoryDto,
  ): Promise<MerchantInventory> {
    const inventory = await this.merchantInventoryModel.findOneAndUpdate(
      { _id: inventoryId, merchantId },
      updateDto,
      { new: true },
    );

    if (!inventory) {
      throw new NotFoundError('Inventory item not found');
    }

    return inventory;
  }

  async removeMerchantInventory(
    merchantId: string,
    inventoryId: string,
  ): Promise<void> {
    const result = await this.merchantInventoryModel.findOneAndDelete({
      _id: inventoryId,
      merchantId,
    });

    if (!result) {
      throw new NotFoundError('Inventory item not found');
    }
  }

  async getMerchantInventoryByProduct(
    merchantId: string,
    universalProductId: string,
  ): Promise<MerchantInventory | null> {
    return this.merchantInventoryModel.findOne({
      merchantId,
      universalProductId,
    });
  }

  async updateStock(
    merchantId: string,
    universalProductId: string,
    stockChange: number,
  ): Promise<MerchantInventory> {
    const inventory = await this.merchantInventoryModel.findOne({
      merchantId,
      universalProductId,
    });

    if (!inventory) {
      throw new NotFoundError('Inventory item not found');
    }

    inventory.stock += stockChange;
    if (inventory.stock < 0) {
      inventory.stock = 0;
    }

    return inventory.save();
  }

  async getLowStockItems(
    merchantId: string,
  ): Promise<MerchantInventory[]> {
    return this.merchantInventoryModel
      .find({
        merchantId,
        $expr: {
          $and: [
            { $ne: ['$minStock', null] },
            { $lte: ['$stock', '$minStock'] },
          ],
        },
      })
      .populate('universalProductId');
  }
}

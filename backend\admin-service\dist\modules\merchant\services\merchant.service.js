"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.MerchantService = void 0;
const common_1 = require("@nestjs/common");
const microservices_1 = require("@nestjs/microservices");
const rxjs_1 = require("rxjs");
const shared_1 = require("../../../constants/shared");
let MerchantService = class MerchantService {
    constructor(merchantClient) {
        this.merchantClient = merchantClient;
    }
    async findAll(query) {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('find_all_merchants', query));
    }
    async findById(id) {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('find_merchant_by_id', { id }));
    }
    async updateMerchantStatus(id, status) {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('update_merchant_status', { id, status }));
    }
    async deleteMerchant(id) {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('delete_merchant', { id }));
    }
    async getMerchantInventory(merchantId, query) {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('get_merchant_inventory', { merchantId, ...query }));
    }
    async getMerchantAnalytics() {
        return (0, rxjs_1.firstValueFrom)(this.merchantClient.send('get_merchant_analytics', {}));
    }
};
exports.MerchantService = MerchantService;
exports.MerchantService = MerchantService = __decorate([
    (0, common_1.Injectable)(),
    __param(0, (0, common_1.Inject)(shared_1.SERVICES.MERCHANT)),
    __metadata("design:paramtypes", [microservices_1.ClientProxy])
], MerchantService);
//# sourceMappingURL=merchant.service.js.map
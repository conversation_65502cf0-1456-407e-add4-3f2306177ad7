import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kqick_merchant/core/theme/app_colors.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_bloc.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_event.dart';
import 'package:kqick_merchant/features/universal_catalog/presentation/bloc/universal_catalog_state.dart';

class UniversalProductFilters extends StatefulWidget {
  final String? selectedCategory;
  final String? selectedSubcategory;
  final String? selectedBrand;
  final double? minPrice;
  final double? maxPrice;
  final Function({
    String? category,
    String? subcategory,
    String? brand,
    double? minPrice,
    double? maxPrice,
  }) onFiltersChanged;

  const UniversalProductFilters({
    super.key,
    this.selectedCategory,
    this.selectedSubcategory,
    this.selectedBrand,
    this.minPrice,
    this.maxPrice,
    required this.onFiltersChanged,
  });

  @override
  State<UniversalProductFilters> createState() => _UniversalProductFiltersState();
}

class _UniversalProductFiltersState extends State<UniversalProductFilters> {
  String? _selectedCategory;
  String? _selectedSubcategory;
  String? _selectedBrand;
  final TextEditingController _minPriceController = TextEditingController();
  final TextEditingController _maxPriceController = TextEditingController();

  List<String> _categories = [];
  List<String> _subcategories = [];
  List<String> _brands = [];

  @override
  void initState() {
    super.initState();
    _selectedCategory = widget.selectedCategory;
    _selectedSubcategory = widget.selectedSubcategory;
    _selectedBrand = widget.selectedBrand;
    _minPriceController.text = widget.minPrice?.toString() ?? '';
    _maxPriceController.text = widget.maxPrice?.toString() ?? '';

    // Load initial data
    context.read<UniversalCatalogBloc>().add(const LoadCategories());
    if (_selectedCategory != null) {
      context.read<UniversalCatalogBloc>().add(LoadSubcategories(_selectedCategory));
      context.read<UniversalCatalogBloc>().add(LoadBrands(category: _selectedCategory));
    }
  }

  @override
  void dispose() {
    _minPriceController.dispose();
    _maxPriceController.dispose();
    super.dispose();
  }

  void _onCategoryChanged(String? category) {
    setState(() {
      _selectedCategory = category;
      _selectedSubcategory = null;
      _selectedBrand = null;
      _subcategories.clear();
      _brands.clear();
    });

    if (category != null) {
      context.read<UniversalCatalogBloc>().add(LoadSubcategories(category));
      context.read<UniversalCatalogBloc>().add(LoadBrands(category: category));
    }
  }

  void _onSubcategoryChanged(String? subcategory) {
    setState(() {
      _selectedSubcategory = subcategory;
      _selectedBrand = null;
      _brands.clear();
    });

    context.read<UniversalCatalogBloc>().add(
      LoadBrands(category: _selectedCategory, subcategory: subcategory),
    );
  }

  void _applyFilters() {
    final minPrice = double.tryParse(_minPriceController.text);
    final maxPrice = double.tryParse(_maxPriceController.text);

    widget.onFiltersChanged(
      category: _selectedCategory,
      subcategory: _selectedSubcategory,
      brand: _selectedBrand,
      minPrice: minPrice,
      maxPrice: maxPrice,
    );

    Navigator.pop(context);
  }

  void _clearFilters() {
    setState(() {
      _selectedCategory = null;
      _selectedSubcategory = null;
      _selectedBrand = null;
      _minPriceController.clear();
      _maxPriceController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(24),
        constraints: const BoxConstraints(maxHeight: 600),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filter Products',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 24),
            Expanded(
              child: SingleChildScrollView(
                child: BlocListener<UniversalCatalogBloc, UniversalCatalogState>(
                  listener: (context, state) {
                    if (state is CategoriesLoaded) {
                      setState(() => _categories = state.categories);
                    } else if (state is SubcategoriesLoaded) {
                      setState(() => _subcategories = state.subcategories);
                    } else if (state is BrandsLoaded) {
                      setState(() => _brands = state.brands);
                    }
                  },
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDropdownField(
                        label: 'Category',
                        value: _selectedCategory,
                        items: _categories,
                        onChanged: _onCategoryChanged,
                      ),
                      const SizedBox(height: 16),
                      _buildDropdownField(
                        label: 'Subcategory',
                        value: _selectedSubcategory,
                        items: _subcategories,
                        onChanged: _onSubcategoryChanged,
                        enabled: _selectedCategory != null,
                      ),
                      const SizedBox(height: 16),
                      _buildDropdownField(
                        label: 'Brand',
                        value: _selectedBrand,
                        items: _brands,
                        onChanged: (brand) => setState(() => _selectedBrand = brand),
                        enabled: _selectedCategory != null,
                      ),
                      const SizedBox(height: 24),
                      Text(
                        'Price Range',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _minPriceController,
                              decoration: const InputDecoration(
                                labelText: 'Min Price',
                                prefixText: 'R',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Expanded(
                            child: TextField(
                              controller: _maxPriceController,
                              decoration: const InputDecoration(
                                labelText: 'Max Price',
                                prefixText: 'R',
                                border: OutlineInputBorder(),
                              ),
                              keyboardType: TextInputType.number,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ),
            const SizedBox(height: 24),
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _clearFilters,
                    child: const Text('Clear All'),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primary,
                      foregroundColor: Colors.white,
                    ),
                    child: const Text('Apply Filters'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField({
    required String label,
    required String? value,
    required List<String> items,
    required ValueChanged<String?> onChanged,
    bool enabled = true,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.titleMedium,
        ),
        const SizedBox(height: 8),
        DropdownButtonFormField<String>(
          value: value,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          hint: Text('Select $label'),
          items: [
            DropdownMenuItem<String>(
              value: null,
              child: Text('All ${label}s'),
            ),
            ...items.map(
              (item) => DropdownMenuItem<String>(
                value: item,
                child: Text(item),
              ),
            ),
          ],
          onChanged: enabled ? onChanged : null,
        ),
      ],
    );
  }
}

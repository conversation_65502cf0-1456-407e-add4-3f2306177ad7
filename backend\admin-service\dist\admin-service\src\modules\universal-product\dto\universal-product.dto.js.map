{"version": 3, "file": "universal-product.dto.js", "sourceRoot": "", "sources": ["../../../../../../src/modules/universal-product/dto/universal-product.dto.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAAA,qDAAqG;AACrG,yDAAyC;AAEzC,MAAa,iBAAiB;CAe7B;AAfD,8CAeC;AAbC;IADC,IAAA,0BAAQ,GAAE;;6CACA;AAGX;IADC,IAAA,0BAAQ,GAAE;;+CACE;AAGb;IADC,IAAA,0BAAQ,GAAE;;gDACG;AAGd;IADC,IAAA,0BAAQ,GAAE;;gDACG;AAGd;IADC,IAAA,2BAAS,GAAE;;sDACS;AAGvB,MAAa,yBAAyB;CA+CrC;AA/CD,8DA+CC;AA7CC;IADC,IAAA,0BAAQ,GAAE;;uDACE;AAGb;IADC,IAAA,0BAAQ,GAAE;;8DACS;AAGpB;IADC,IAAA,0BAAQ,GAAE;;2DACM;AAGjB;IADC,IAAA,0BAAQ,GAAE;;8DACS;AAGpB;IADC,IAAA,0BAAQ,GAAE;;wDACG;AAGd;IADC,IAAA,0BAAQ,GAAE;;iEACY;AAIvB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACE;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC;;2DACkB;AAIhD;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;2DACO;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAGhB;IADC,IAAA,4BAAU,GAAE;;iEAC2B;AAG1C,MAAa,yBAAyB;CAqDrC;AArDD,8DAqDC;AAlDC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;iEACa;AAIxB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;0DACM;AAIjB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;sDACE;AAKb;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,gCAAc,GAAE;IAChB,IAAA,wBAAI,EAAC,GAAG,EAAE,CAAC,iBAAiB,CAAC;;2DACkB;AAIhD;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;2DACO;AAKnB;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAGhB;IADC,IAAA,4BAAU,GAAE;;iEAC2B;AAG1C,MAAa,yBAAyB;CAiDrC;AAjDD,8DAiDC;AA9CC;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;8DACU;AAIrB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAKf;IAHC,IAAA,4BAAU,GAAE;IACZ,IAAA,yBAAO,GAAE;IACT,IAAA,0BAAQ,EAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC;;uDACT;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;2DACO;AAIlB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,2BAAS,GAAE;;2DACO;AAInB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;uDACG;AAId;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;wDACI;AAIf;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;yDACK;AAIhB;IAFC,IAAA,4BAAU,GAAE;IACZ,IAAA,0BAAQ,GAAE;;4DACgB"}
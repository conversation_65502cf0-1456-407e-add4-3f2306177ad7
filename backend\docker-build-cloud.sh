#!/bin/bash

# KQICK Docker Build Cloud Script
# This script builds all microservices using Docker Build Cloud

set -e

# Configuration
BUILDER_NAME="kqick-cloud-builder"
REGISTRY="kqick00"
TAG=${1:-latest}

echo "🚀 KQICK Docker Build Cloud Setup"
echo "=================================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Check if buildx is available
if ! docker buildx version &> /dev/null; then
    echo "❌ Docker Buildx is not available"
    exit 1
fi

# Create cloud builder if it doesn't exist
echo "🔧 Setting up cloud builder..."
if ! docker buildx ls | grep -q "$BUILDER_NAME"; then
    echo "Creating new cloud builder: $BUILDER_NAME"
    docker buildx create --driver cloud $REGISTRY/kqick --name $BUILDER_NAME
else
    echo "Cloud builder $BUILDER_NAME already exists"
fi

# Use the cloud builder
echo "🎯 Using cloud builder: $BUILDER_NAME"
docker buildx use $BUILDER_NAME

# Build services
echo "🏗️  Building KQICK microservices..."

# Build shared library first
echo "📦 Building shared library..."
cd shared-lib
docker buildx build \
    --platform linux/amd64,linux/arm64 \
    --tag $REGISTRY/kqick-shared-lib:$TAG \
    --push \
    .
cd ..

# Build each service
services=("auth-service" "product-service" "admin-service" "api-gateway")

for service in "${services[@]}"; do
    if [ -d "$service" ]; then
        echo "🔨 Building $service..."
        cd $service
        
        # Build and push multi-platform image
        docker buildx build \
            --platform linux/amd64,linux/arm64 \
            --tag $REGISTRY/kqick-$service:$TAG \
            --tag $REGISTRY/kqick-$service:latest \
            --push \
            .
        
        cd ..
        echo "✅ $service built successfully"
    else
        echo "⚠️  $service directory not found, skipping..."
    fi
done

echo ""
echo "🎉 All KQICK services built successfully!"
echo "📋 Built images:"
for service in "${services[@]}"; do
    echo "   - $REGISTRY/kqick-$service:$TAG"
done
echo "   - $REGISTRY/kqick-shared-lib:$TAG"
echo ""
echo "🚀 Ready to deploy with docker-compose or Kubernetes!"

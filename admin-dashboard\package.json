{"name": "kqick-admin-dashboard", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.3", "@mui/x-data-grid": "^6.10.1", "@mui/x-date-pickers": "^6.10.1", "@reduxjs/toolkit": "^1.9.5", "@types/node": "^20.4.5", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "axios": "^1.4.0", "date-fns": "^2.30.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.2", "react-query": "^3.39.3", "react-redux": "^8.1.2", "react-router-dom": "^6.14.2", "react-scripts": "5.0.1", "recharts": "^2.7.2", "typescript": "^5.1.6", "web-vitals": "^3.3.2"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/jest": "^29.5.3"}, "proxy": "http://localhost:3004"}
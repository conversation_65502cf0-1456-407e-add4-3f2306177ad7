{"name": "winston-transport", "description": "Base stream implementations for winston@3 and up.", "version": "4.9.0", "main": "index.js", "browser": "dist/index.js", "scripts": {"lint": "ESLINT_USE_FLAT_CONFIG=false eslint test/*.js index.js --resolve-plugins-relative-to ./node_modules/@dabh/eslint-config-populist", "pretest": "npm run lint && npm run build", "test": "nyc mocha test/*.test.js", "report": "nyc report --reporter=lcov", "build": "rimraf dist && babel *.js -d ./dist", "prepublishOnly": "npm run build"}, "repository": {"type": "git", "url": "**************:winstonjs/winston-transport.git"}, "keywords": ["winston", "transport", "winston3"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/winstonjs/winston-transport/issues"}, "homepage": "https://github.com/winstonjs/winston-transport#readme", "dependencies": {"logform": "^2.7.0", "readable-stream": "^3.6.2", "triple-beam": "^1.3.0"}, "devDependencies": {"@types/node": "^22.6.0", "abstract-winston-transport": ">=0.5.1", "assume": "^2.3.0", "babel-cli": "^6.26.0", "babel-preset-env": "^1.7.0", "deep-equal": "^2.0.5", "eslint": "^9.6.0", "@dabh/eslint-config-populist": "^4.4.0", "mocha": "^10.6.0", "nyc": "^17.0.0", "rimraf": "^6.0.1", "winston-compat": "^0.1.5"}, "engines": {"node": ">= 12.0.0"}}
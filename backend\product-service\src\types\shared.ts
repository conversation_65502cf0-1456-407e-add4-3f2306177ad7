// Temporary types to replace @kqick/shared-lib

export class ProductVariant {
  id: string;
  name: string;
  price: number;
  sku: string;
  stock: number;
  attributes: Record<string, any>;
}

export interface SearchQuery {
  query?: string;
  search?: string;
  category?: string;
  minPrice?: number;
  maxPrice?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sort?: string;
  sortOrder?: 'asc' | 'desc';
  order?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasMore?: boolean;
}

export class NotFoundError extends Error {
  constructor(message: string) {
    super(message);
    this.name = 'NotFoundError';
  }
}

export const TOPICS = {
  PRODUCT_CREATED: 'product.created',
  PRODUCT_UPDATED: 'product.updated',
  PRODUCT_DELETED: 'product.deleted',
  INVENTORY_UPDATED: 'inventory.updated',
  UNIVERSAL_PRODUCT_CREATED: 'universal.product.created',
  UNIVERSAL_PRODUCT_UPDATED: 'universal.product.updated',
  UNIVERSAL_PRODUCT_DELETED: 'universal.product.deleted',
  MERCHANT_INVENTORY_CREATED: 'merchant.inventory.created',
  MERCHANT_INVENTORY_UPDATED: 'merchant.inventory.updated',
  MERCHANT_INVENTORY_DELETED: 'merchant.inventory.deleted',
} as const;

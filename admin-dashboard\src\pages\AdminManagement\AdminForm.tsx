import React, { useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Box,
  FormGroup,
  Checkbox,
} from '@mui/material';
import { useForm, Controller } from 'react-hook-form';
import { useMutation } from 'react-query';
import { apiService } from '../../services/api';

interface AdminFormProps {
  open: boolean;
  onClose: () => void;
  admin?: any;
  onSuccess: () => void;
}

interface AdminFormData {
  email: string;
  password?: string;
  firstName: string;
  lastName: string;
  role: 'super_admin' | 'admin' | 'moderator';
  permissions: string[];
  isActive: boolean;
  phoneNumber?: string;
}

const AVAILABLE_PERMISSIONS = [
  'manage_products',
  'manage_merchants',
  'manage_orders',
  'view_analytics',
  'manage_admins',
  'system_settings',
];

const ROLE_PERMISSIONS = {
  super_admin: AVAILABLE_PERMISSIONS,
  admin: ['manage_products', 'manage_merchants', 'manage_orders', 'view_analytics'],
  moderator: ['manage_products', 'view_analytics'],
};

const AdminForm: React.FC<AdminFormProps> = ({
  open,
  onClose,
  admin,
  onSuccess,
}) => {
  const isEdit = !!admin;

  const { control, handleSubmit, reset, watch, setValue } = useForm<AdminFormData>({
    defaultValues: {
      email: '',
      password: '',
      firstName: '',
      lastName: '',
      role: 'moderator',
      permissions: [],
      isActive: true,
      phoneNumber: '',
    },
  });

  const selectedRole = watch('role');
  const selectedPermissions = watch('permissions');

  const createMutation = useMutation(apiService.createAdmin, {
    onSuccess,
  });

  const updateMutation = useMutation(
    ({ id, data }: { id: string; data: any }) =>
      apiService.updateAdmin(id, data),
    { onSuccess }
  );

  useEffect(() => {
    if (admin) {
      reset({
        email: admin.email || '',
        firstName: admin.firstName || '',
        lastName: admin.lastName || '',
        role: admin.role || 'moderator',
        permissions: admin.permissions || [],
        isActive: admin.isActive ?? true,
        phoneNumber: admin.phoneNumber || '',
      });
    } else {
      reset({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        role: 'moderator',
        permissions: ROLE_PERMISSIONS.moderator,
        isActive: true,
        phoneNumber: '',
      });
    }
  }, [admin, reset]);

  useEffect(() => {
    // Update permissions based on role
    if (selectedRole) {
      setValue('permissions', ROLE_PERMISSIONS[selectedRole]);
    }
  }, [selectedRole, setValue]);

  const onSubmit = (data: AdminFormData) => {
    const formData = { ...data };

    // Remove password if it's empty for edit
    if (isEdit && !formData.password) {
      delete formData.password;
    }

    if (isEdit) {
      updateMutation.mutate({ id: admin.id, data: formData });
    } else {
      createMutation.mutate(formData);
    }
  };

  const handlePermissionChange = (permission: string, checked: boolean) => {
    const currentPermissions = selectedPermissions || [];
    if (checked) {
      setValue('permissions', [...currentPermissions, permission]);
    } else {
      setValue('permissions', currentPermissions.filter(p => p !== permission));
    }
  };

  const isLoading = createMutation.isLoading || updateMutation.isLoading;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{isEdit ? 'Edit Admin' : 'Add New Admin'}</DialogTitle>
      <form onSubmit={handleSubmit(onSubmit)}>
        <DialogContent>
          <Grid container spacing={2}>
            <Grid item xs={12} md={6}>
              <Controller
                name="firstName"
                control={control}
                rules={{ required: 'First name is required' }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="First Name"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="lastName"
                control={control}
                rules={{ required: 'Last name is required' }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Last Name"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="email"
                control={control}
                rules={{
                  required: 'Email is required',
                  pattern: {
                    value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                    message: 'Invalid email address',
                  },
                }}
                render={({ field, fieldState }) => (
                  <TextField
                    {...field}
                    fullWidth
                    label="Email"
                    type="email"
                    error={!!fieldState.error}
                    helperText={fieldState.error?.message}
                  />
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field }) => (
                  <TextField {...field} fullWidth label="Phone Number" />
                )}
              />
            </Grid>
            {!isEdit && (
              <Grid item xs={12}>
                <Controller
                  name="password"
                  control={control}
                  rules={{
                    required: !isEdit ? 'Password is required' : false,
                    minLength: {
                      value: 8,
                      message: 'Password must be at least 8 characters',
                    },
                  }}
                  render={({ field, fieldState }) => (
                    <TextField
                      {...field}
                      fullWidth
                      label="Password"
                      type="password"
                      error={!!fieldState.error}
                      helperText={fieldState.error?.message}
                    />
                  )}
                />
              </Grid>
            )}
            <Grid item xs={12} md={6}>
              <Controller
                name="role"
                control={control}
                rules={{ required: 'Role is required' }}
                render={({ field, fieldState }) => (
                  <FormControl fullWidth error={!!fieldState.error}>
                    <InputLabel>Role</InputLabel>
                    <Select {...field} label="Role">
                      <MenuItem value="moderator">Moderator</MenuItem>
                      <MenuItem value="admin">Admin</MenuItem>
                      <MenuItem value="super_admin">Super Admin</MenuItem>
                    </Select>
                  </FormControl>
                )}
              />
            </Grid>
            <Grid item xs={12} md={6}>
              <Controller
                name="isActive"
                control={control}
                render={({ field }) => (
                  <FormControlLabel
                    control={<Switch {...field} checked={field.value} />}
                    label="Active"
                  />
                )}
              />
            </Grid>
            <Grid item xs={12}>
              <Box>
                <InputLabel sx={{ mb: 1 }}>Permissions</InputLabel>
                <FormGroup row>
                  {AVAILABLE_PERMISSIONS.map((permission) => (
                    <FormControlLabel
                      key={permission}
                      control={
                        <Checkbox
                          checked={selectedPermissions?.includes(permission) || false}
                          onChange={(e) => handlePermissionChange(permission, e.target.checked)}
                        />
                      }
                      label={permission.replace('_', ' ').toUpperCase()}
                    />
                  ))}
                </FormGroup>
              </Box>
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={onClose}>Cancel</Button>
          <Button type="submit" variant="contained" disabled={isLoading}>
            {isEdit ? 'Update' : 'Create'}
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};

export default AdminForm;

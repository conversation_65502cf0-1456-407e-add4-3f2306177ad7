export declare class ProductVariant {
    id: string;
    name: string;
    price: number;
    sku: string;
    stock: number;
    attributes: Record<string, any>;
}
export interface SearchQuery {
    query?: string;
    search?: string;
    category?: string;
    minPrice?: number;
    maxPrice?: number;
    page?: number;
    limit?: number;
    sortBy?: string;
    sort?: string;
    sortOrder?: 'asc' | 'desc';
    order?: 'asc' | 'desc';
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasMore?: boolean;
}
export declare class NotFoundError extends Error {
    constructor(message: string);
}
export declare const TOPICS: {
    readonly PRODUCT_CREATED: "product.created";
    readonly PRODUCT_UPDATED: "product.updated";
    readonly PRODUCT_DELETED: "product.deleted";
    readonly INVENTORY_UPDATED: "inventory.updated";
    readonly UNIVERSAL_PRODUCT_CREATED: "universal.product.created";
    readonly UNIVERSAL_PRODUCT_UPDATED: "universal.product.updated";
    readonly UNIVERSAL_PRODUCT_DELETED: "universal.product.deleted";
    readonly MERCHANT_INVENTORY_CREATED: "merchant.inventory.created";
    readonly MERCHANT_INVENTORY_UPDATED: "merchant.inventory.updated";
    readonly MERCHANT_INVENTORY_DELETED: "merchant.inventory.deleted";
};

import { ClientProxy } from '@nestjs/microservices';
export declare class MerchantService {
    private readonly merchantClient;
    constructor(merchantClient: ClientProxy);
    findAll(query: {
        page?: number;
        limit?: number;
        search?: string;
        status?: string;
    }): Promise<any>;
    findById(id: string): Promise<any>;
    updateMerchantStatus(id: string, status: string): Promise<any>;
    deleteMerchant(id: string): Promise<any>;
    getMerchantInventory(merchantId: string, query: {
        page?: number;
        limit?: number;
    }): Promise<any>;
    getMerchantAnalytics(): Promise<any>;
}

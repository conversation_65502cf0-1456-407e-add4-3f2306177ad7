// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

UniversalProduct _$UniversalProductFromJson(Map<String, dynamic> json) =>
    UniversalProduct(
      id: json['id'] as String,
      name: json['name'] as String,
      description: json['description'] as String,
      category: json['category'] as String,
      subcategory: json['subcategory'] as String,
      brand: json['brand'] as String,
      suggestedPrice: (json['suggestedPrice'] as num).toDouble(),
      imageUrl: json['imageUrl'] as String?,
      barcode: json['barcode'] as String?,
      sku: json['sku'] as String?,
      variants: json['variants'] as Map<String, dynamic>?,
      isActive: json['isActive'] as bool? ?? true,
      tags:
          (json['tags'] as List<dynamic>?)?.map((e) => e as String).toList() ??
          const [],
      specifications: json['specifications'] as Map<String, dynamic>?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$UniversalProductToJson(UniversalProduct instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'description': instance.description,
      'category': instance.category,
      'subcategory': instance.subcategory,
      'brand': instance.brand,
      'suggestedPrice': instance.suggestedPrice,
      'imageUrl': instance.imageUrl,
      'barcode': instance.barcode,
      'sku': instance.sku,
      'variants': instance.variants,
      'isActive': instance.isActive,
      'tags': instance.tags,
      'specifications': instance.specifications,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

MerchantInventory _$MerchantInventoryFromJson(Map<String, dynamic> json) =>
    MerchantInventory(
      id: json['id'] as String,
      merchantId: json['merchantId'] as String,
      universalProductId: json['universalProductId'] as String,
      price: (json['price'] as num).toDouble(),
      stock: (json['stock'] as num).toInt(),
      isAvailable: json['isAvailable'] as bool? ?? true,
      minStock: (json['minStock'] as num?)?.toInt(),
      maxStock: (json['maxStock'] as num?)?.toInt(),
      location: json['location'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MerchantInventoryToJson(MerchantInventory instance) =>
    <String, dynamic>{
      'id': instance.id,
      'merchantId': instance.merchantId,
      'universalProductId': instance.universalProductId,
      'price': instance.price,
      'stock': instance.stock,
      'isAvailable': instance.isAvailable,
      'minStock': instance.minStock,
      'maxStock': instance.maxStock,
      'location': instance.location,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

MerchantInventoryWithProduct _$MerchantInventoryWithProductFromJson(
  Map<String, dynamic> json,
) => MerchantInventoryWithProduct(
  inventory: MerchantInventory.fromJson(
    json['inventory'] as Map<String, dynamic>,
  ),
  product: UniversalProduct.fromJson(json['product'] as Map<String, dynamic>),
);

Map<String, dynamic> _$MerchantInventoryWithProductToJson(
  MerchantInventoryWithProduct instance,
) => <String, dynamic>{
  'inventory': instance.inventory,
  'product': instance.product,
};

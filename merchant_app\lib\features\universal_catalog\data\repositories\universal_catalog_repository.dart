import 'package:dio/dio.dart';
import 'package:kqick_merchant/core/network/api_client.dart';
import 'package:kqick_merchant/shared/models/universal_product.dart';

abstract class UniversalCatalogRepository {
  Future<List<UniversalProduct>> getUniversalProducts({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
    String? subcategory,
    String? brand,
    List<String>? tags,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
  });

  Future<UniversalProduct> getUniversalProductById(String id);
  Future<List<String>> getCategories();
  Future<List<String>> getSubcategories(String? category);
  Future<List<String>> getBrands(String? category, String? subcategory);
  Future<UniversalProduct?> searchByBarcode(String barcode);
  Future<UniversalProduct?> searchBySku(String sku);

  // Merchant Inventory Methods
  Future<MerchantInventory> addProductToInventory({
    required String universalProductId,
    required double price,
    int stock = 0,
    bool isAvailable = true,
    int? minStock,
    int? maxStock,
    String? location,
  });

  Future<List<MerchantInventoryWithProduct>> getMerchantInventory({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
  });

  Future<MerchantInventory> updateMerchantInventory({
    required String inventoryId,
    double? price,
    int? stock,
    bool? isAvailable,
    int? minStock,
    int? maxStock,
    String? location,
  });

  Future<void> removeMerchantInventory(String inventoryId);
  Future<MerchantInventory?> getMerchantInventoryByProduct(String universalProductId);
  Future<MerchantInventory> updateStock(String universalProductId, int stockChange);
  Future<List<MerchantInventoryWithProduct>> getLowStockItems();
}

class UniversalCatalogRepositoryImpl implements UniversalCatalogRepository {
  final ApiClient _apiClient;

  UniversalCatalogRepositoryImpl(this._apiClient);

  @override
  Future<List<UniversalProduct>> getUniversalProducts({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
    String? subcategory,
    String? brand,
    List<String>? tags,
    double? minPrice,
    double? maxPrice,
    String? sortBy,
    String? sortOrder,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (category != null) 'category': category,
        if (subcategory != null) 'subcategory': subcategory,
        if (brand != null) 'brand': brand,
        if (tags != null && tags.isNotEmpty) 'tags': tags,
        if (minPrice != null) 'minPrice': minPrice,
        if (maxPrice != null) 'maxPrice': maxPrice,
        if (sortBy != null) 'sortBy': sortBy,
        if (sortOrder != null) 'sortOrder': sortOrder,
      };

      final response = await _apiClient.get(
        '/universal-catalog/products',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      return data.map((json) => UniversalProduct.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception('Failed to fetch universal products: ${e.message}');
    }
  }

  @override
  Future<UniversalProduct> getUniversalProductById(String id) async {
    try {
      final response = await _apiClient.get('/universal-catalog/products/$id');
      return UniversalProduct.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to fetch universal product: ${e.message}');
    }
  }

  @override
  Future<List<String>> getCategories() async {
    try {
      final response = await _apiClient.get('/universal-catalog/categories');
      return List<String>.from(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to fetch categories: ${e.message}');
    }
  }

  @override
  Future<List<String>> getSubcategories(String? category) async {
    try {
      final queryParams = category != null ? {'category': category} : <String, dynamic>{};
      final response = await _apiClient.get(
        '/universal-catalog/subcategories',
        queryParameters: queryParams,
      );
      return List<String>.from(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to fetch subcategories: ${e.message}');
    }
  }

  @override
  Future<List<String>> getBrands(String? category, String? subcategory) async {
    try {
      final queryParams = <String, dynamic>{};
      if (category != null) queryParams['category'] = category;
      if (subcategory != null) queryParams['subcategory'] = subcategory;

      final response = await _apiClient.get(
        '/universal-catalog/brands',
        queryParameters: queryParams,
      );
      return List<String>.from(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to fetch brands: ${e.message}');
    }
  }

  @override
  Future<UniversalProduct?> searchByBarcode(String barcode) async {
    try {
      final response = await _apiClient.get(
        '/universal-catalog/search/barcode',
        queryParameters: {'barcode': barcode},
      );
      return response.data != null ? UniversalProduct.fromJson(response.data) : null;
    } on DioException catch (e) {
      throw Exception('Failed to search by barcode: ${e.message}');
    }
  }

  @override
  Future<UniversalProduct?> searchBySku(String sku) async {
    try {
      final response = await _apiClient.get(
        '/universal-catalog/search/sku',
        queryParameters: {'sku': sku},
      );
      return response.data != null ? UniversalProduct.fromJson(response.data) : null;
    } on DioException catch (e) {
      throw Exception('Failed to search by SKU: ${e.message}');
    }
  }

  @override
  Future<MerchantInventory> addProductToInventory({
    required String universalProductId,
    required double price,
    int stock = 0,
    bool isAvailable = true,
    int? minStock,
    int? maxStock,
    String? location,
  }) async {
    try {
      final data = {
        'universalProductId': universalProductId,
        'price': price,
        'stock': stock,
        'isAvailable': isAvailable,
        if (minStock != null) 'minStock': minStock,
        if (maxStock != null) 'maxStock': maxStock,
        if (location != null) 'location': location,
      };

      final response = await _apiClient.post('/merchant/inventory', data: data);
      return MerchantInventory.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to add product to inventory: ${e.message}');
    }
  }

  @override
  Future<List<MerchantInventoryWithProduct>> getMerchantInventory({
    int page = 1,
    int limit = 20,
    String? search,
    String? category,
  }) async {
    try {
      final queryParams = <String, dynamic>{
        'page': page,
        'limit': limit,
        if (search != null) 'search': search,
        if (category != null) 'category': category,
      };

      final response = await _apiClient.get(
        '/merchant/inventory',
        queryParameters: queryParams,
      );

      final List<dynamic> data = response.data['data'];
      return data.map((json) => MerchantInventoryWithProduct.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception('Failed to fetch merchant inventory: ${e.message}');
    }
  }

  @override
  Future<MerchantInventory> updateMerchantInventory({
    required String inventoryId,
    double? price,
    int? stock,
    bool? isAvailable,
    int? minStock,
    int? maxStock,
    String? location,
  }) async {
    try {
      final data = <String, dynamic>{};
      if (price != null) data['price'] = price;
      if (stock != null) data['stock'] = stock;
      if (isAvailable != null) data['isAvailable'] = isAvailable;
      if (minStock != null) data['minStock'] = minStock;
      if (maxStock != null) data['maxStock'] = maxStock;
      if (location != null) data['location'] = location;

      final response = await _apiClient.put('/merchant/inventory/$inventoryId', data: data);
      return MerchantInventory.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to update inventory: ${e.message}');
    }
  }

  @override
  Future<void> removeMerchantInventory(String inventoryId) async {
    try {
      await _apiClient.delete('/merchant/inventory/$inventoryId');
    } on DioException catch (e) {
      throw Exception('Failed to remove inventory: ${e.message}');
    }
  }

  @override
  Future<MerchantInventory?> getMerchantInventoryByProduct(String universalProductId) async {
    try {
      final response = await _apiClient.get('/merchant/inventory/product/$universalProductId');
      return response.data != null ? MerchantInventory.fromJson(response.data) : null;
    } on DioException catch (e) {
      throw Exception('Failed to fetch inventory by product: ${e.message}');
    }
  }

  @override
  Future<MerchantInventory> updateStock(String universalProductId, int stockChange) async {
    try {
      final response = await _apiClient.patch(
        '/merchant/inventory/stock/$universalProductId',
        data: {'stockChange': stockChange},
      );
      return MerchantInventory.fromJson(response.data);
    } on DioException catch (e) {
      throw Exception('Failed to update stock: ${e.message}');
    }
  }

  @override
  Future<List<MerchantInventoryWithProduct>> getLowStockItems() async {
    try {
      final response = await _apiClient.get('/merchant/inventory/low-stock');
      final List<dynamic> data = response.data;
      return data.map((json) => MerchantInventoryWithProduct.fromJson(json)).toList();
    } on DioException catch (e) {
      throw Exception('Failed to fetch low stock items: ${e.message}');
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:kqick_merchant/features/products/presentation/bloc/product_bloc.dart';
import 'package:kqick_merchant/features/products/presentation/bloc/product_event.dart';
import 'package:kqick_merchant/features/products/presentation/bloc/product_state.dart';
import 'package:kqick_merchant/features/products/presentation/models/product_sort.dart';
import 'package:kqick_merchant/features/products/presentation/pages/product_details_page.dart';
import 'package:kqick_merchant/features/products/presentation/widgets/category_filter_dialog.dart';
import 'package:kqick_merchant/features/products/presentation/widgets/product_grid_item.dart';
import 'package:kqick_merchant/shared/models/product.dart';

class ProductCatalogPage extends StatefulWidget {
  const ProductCatalogPage({super.key});

  @override
  State<ProductCatalogPage> createState() => _ProductCatalogPageState();
}

class _ProductCatalogPageState extends State<ProductCatalogPage> {
  final _scrollController = ScrollController();
  String? _searchQuery;
  String? _selectedCategory;
  ProductSort? _currentSort;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
    _loadProducts();
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _loadProducts({bool refresh = false}) {
    context.read<ProductBloc>().add(
      LoadProducts(
        refresh: refresh,
        search: _searchQuery,
        category: _selectedCategory,
        sort: _currentSort,
      ),
    );
  }

  void _onScroll() {
    if (_isBottom) {
      context.read<ProductBloc>().add(LoadMoreProducts());
    }
  }

  bool get _isBottom {
    if (!_scrollController.hasClients) return false;
    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;
    return currentScroll >= (maxScroll * 0.9);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Universal Product Catalog'),
        actions: [
          PopupMenuButton<ProductSort>(
            tooltip: 'Sort products',
            icon: Icon(_currentSort != null ? Icons.sort : Icons.sort_outlined),
            onSelected: (sort) {
              if (_currentSort == sort) {
                setState(() => _currentSort = null);
              } else {
                setState(() => _currentSort = sort);
              }
              _loadProducts(refresh: true);
            },
            itemBuilder:
                (context) =>
                    ProductSort.values
                        .map(
                          (sort) => PopupMenuItem(
                            value: sort,
                            child: Row(
                              children: [
                                if (_currentSort == sort)
                                  const Icon(Icons.check, size: 18)
                                else
                                  const SizedBox(width: 18),
                                const SizedBox(width: 8),
                                Text(sort.label),
                              ],
                            ),
                          ),
                        )
                        .toList(),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: _showFilterDialog,
          ),
        ],
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: TextField(
              decoration: const InputDecoration(
                hintText: 'Search products...',
                prefixIcon: Icon(Icons.search),
                border: OutlineInputBorder(),
              ),
              onChanged: (value) {
                setState(() => _searchQuery = value);
                _loadProducts(refresh: true);
              },
            ),
          ),
          if (_selectedCategory != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  const Text('Category: '),
                  Chip(
                    label: Text(_selectedCategory!),
                    onDeleted: () {
                      setState(() => _selectedCategory = null);
                      _loadProducts(refresh: true);
                    },
                  ),
                ],
              ),
            ),
          if (_currentSort != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Row(
                children: [
                  const Text('Sort: '),
                  Chip(
                    label: Text(_currentSort!.label),
                    onDeleted: () {
                      setState(() => _currentSort = null);
                      _loadProducts(refresh: true);
                    },
                  ),
                ],
              ),
            ),
          Expanded(
            child: BlocBuilder<ProductBloc, ProductState>(
              builder: (context, state) {
                if (state is ProductLoading) {
                  return const Center(child: CircularProgressIndicator());
                }

                if (state is ProductError) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(state.message),
                        const SizedBox(height: 16),
                        ElevatedButton(
                          onPressed: () => _loadProducts(),
                          child: const Text('Retry'),
                        ),
                      ],
                    ),
                  );
                }

                if (state is ProductsLoaded) {
                  if (state.products.isEmpty) {
                    return const Center(child: Text('No products found'));
                  }

                  return RefreshIndicator(
                    onRefresh: () async {
                      _loadProducts(refresh: true);
                    },
                    child: GridView.builder(
                      controller: _scrollController,
                      padding: const EdgeInsets.all(16),
                      gridDelegate:
                          const SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 2,
                            childAspectRatio: 0.75,
                            mainAxisSpacing: 16,
                            crossAxisSpacing: 16,
                          ),
                      itemCount:
                          state.hasReachedMax
                              ? state.products.length
                              : state.products.length + 1,
                      itemBuilder: (context, index) {
                        if (index >= state.products.length) {
                          return const Center(
                            child: CircularProgressIndicator(),
                          );
                        }
                        final product = state.products[index];
                        return ProductGridItem(
                          product: product,
                          onTap: () => _showProductDetails(product),
                        );
                      },
                    ),
                  );
                }

                return const SizedBox.shrink();
              },
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _showFilterDialog() async {
    final state = context.read<ProductBloc>().state;
    if (state is ProductsLoaded) {
      final categories =
          state.products
              .map((product) => product.category)
              .where((category) => category.isNotEmpty)
              .toSet()
              .toList()
            ..sort();

      final selectedCategory = await showDialog<String?>(
        context: context,
        builder:
            (context) => CategoryFilterDialog(
              selectedCategory: _selectedCategory,
              categories: categories,
            ),
      );

      if (selectedCategory != _selectedCategory) {
        setState(() => _selectedCategory = selectedCategory);
        _loadProducts(refresh: true);
      }
    }
  }

  void _showProductDetails(Product product) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => ProductDetailsPage(product: product),
      ),
    );
  }
}
